#!/usr/bin/env python3
"""
Build script for Lambda deployment package
Creates a ZIP file with the Lambda function and its dependencies
"""

import os
import shutil
import subprocess
import sys
import zipfile
from pathlib import Path

def build_lambda_package():
    """Build the Lambda deployment package"""
    
    # Configuration
    package_dir = "lambda_package"
    zip_filename = "lambda-authorizer.zip"
    
    print("Building Lambda deployment package...")
    
    # Clean up previous build
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    if os.path.exists(zip_filename):
        os.remove(zip_filename)
    
    # Create package directory
    os.makedirs(package_dir)
    
    # Install dependencies
    print("Installing dependencies...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "-r", "requirements.txt", 
            "-t", package_dir,
            "--no-deps"  # Don't install sub-dependencies to keep package small
        ], check=True)
        
        # Install specific dependencies that are needed
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "PyJWT==2.8.0", 
            "-t", package_dir,
            "--no-deps"
        ], check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        return False
    
    # Copy Lambda function
    print("Copying Lambda function...")
    shutil.copy2("lambda_function.py", package_dir)
    
    # Create ZIP file
    print("Creating ZIP package...")
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, package_dir)
                zipf.write(file_path, arcname)
    
    # Clean up temporary directory
    shutil.rmtree(package_dir)
    
    # Check ZIP file size
    zip_size = os.path.getsize(zip_filename)
    print(f"Package created: {zip_filename} ({zip_size / 1024 / 1024:.2f} MB)")
    
    if zip_size > 50 * 1024 * 1024:  # 50MB limit for direct upload
        print("Warning: Package size exceeds 50MB. Consider using S3 for deployment.")
    
    return True

if __name__ == "__main__":
    success = build_lambda_package()
    if success:
        print("Lambda package built successfully!")
        sys.exit(0)
    else:
        print("Failed to build Lambda package!")
        sys.exit(1)
