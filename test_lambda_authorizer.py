"""
Comprehensive tests for the enhanced Lambda Authorizer
Tests token expiration checks, detailed logging, and robust error handling
"""

import unittest
import json
import time
import jwt
import hashlib
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone, timedelta

# Import the lambda function (assuming it's in the same directory)
import lambda_function


class TestLambdaAuthorizer(unittest.TestCase):
    """Test cases for enhanced Lambda Authorizer functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_jwt_secret = "test-secret-key-for-testing-purposes-only"
        self.test_correlation_id = "test-correlation-123"
        
        # Mock AWS services
        self.mock_dynamodb = Mock()
        self.mock_secretsmanager = Mock()
        
        # Sample event structure
        self.sample_event = {
            'methodArn': 'arn:aws:execute-api:us-east-1:123456789012:abcdef123/test/GET/resource',
            'authorizationToken': 'Bearer test-token',
            'requestContext': {
                'identity': {
                    'sourceIp': '***********',
                    'userAgent': 'Mozilla/5.0 Test Browser'
                }
            }
        }
        
        self.sample_context = Mock()
        self.sample_context.aws_request_id = 'test-request-id-123'

    def test_auth_error_enum(self):
        """Test AuthError enum values"""
        self.assertEqual(lambda_function.AuthError.MISSING_TOKEN.value, "missing_token")
        self.assertEqual(lambda_function.AuthError.TOKEN_EXPIRED.value, "token_expired")
        self.assertEqual(lambda_function.AuthError.API_KEY_NOT_FOUND.value, "api_key_not_found")

    def test_auth_exception(self):
        """Test AuthException custom exception"""
        error = lambda_function.AuthException(
            lambda_function.AuthError.TOKEN_EXPIRED,
            "Test token expired",
            {"token_id": "test123"}
        )
        
        self.assertEqual(error.error_type, lambda_function.AuthError.TOKEN_EXPIRED)
        self.assertEqual(error.message, "Test token expired")
        self.assertEqual(error.details["token_id"], "test123")

    def test_log_auth_event(self):
        """Test enhanced logging functionality"""
        with patch('lambda_function.logger') as mock_logger:
            lambda_function.log_auth_event(
                'INFO',
                'Test message',
                self.test_correlation_id,
                lambda_function.AuthError.TOKEN_EXPIRED,
                {'test_detail': 'value'}
            )
            
            # Verify logger was called
            mock_logger.info.assert_called_once()
            
            # Parse the logged message
            logged_data = json.loads(mock_logger.info.call_args[0][0])
            self.assertEqual(logged_data['message'], 'Test message')
            self.assertEqual(logged_data['correlation_id'], self.test_correlation_id)
            self.assertEqual(logged_data['error_type'], 'token_expired')
            self.assertEqual(logged_data['test_detail'], 'value')

    def test_get_error_category(self):
        """Test error categorization"""
        self.assertEqual(
            lambda_function._get_error_category(lambda_function.AuthError.TOKEN_EXPIRED),
            'token_error'
        )
        self.assertEqual(
            lambda_function._get_error_category(lambda_function.AuthError.API_KEY_NOT_FOUND),
            'api_key_error'
        )
        self.assertEqual(
            lambda_function._get_error_category(lambda_function.AuthError.RATE_LIMIT_EXCEEDED),
            'rate_limit_error'
        )

    def test_validate_token_format_jwt(self):
        """Test JWT token format validation"""
        # Valid JWT format
        valid_jwt = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWV9.TJVA95OrM7E2cBab30RMHrHDcEfxjoYZgeFONFh7HgQ"
        is_valid, message = lambda_function.validate_token_format(valid_jwt, 'jwt')
        self.assertTrue(is_valid)
        self.assertEqual(message, "Valid JWT format")
        
        # Invalid JWT format (only 2 parts)
        invalid_jwt = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWV9"
        is_valid, message = lambda_function.validate_token_format(invalid_jwt, 'jwt')
        self.assertFalse(is_valid)
        self.assertIn("should have 3 parts", message)

    def test_validate_token_format_api_key(self):
        """Test API key format validation"""
        # Valid API key
        valid_api_key = "abcdef1234567890abcdef1234567890abcdef12"
        is_valid, message = lambda_function.validate_token_format(valid_api_key, 'api_key')
        self.assertTrue(is_valid)
        self.assertEqual(message, "Valid API key format")
        
        # Too short API key
        short_api_key = "short"
        is_valid, message = lambda_function.validate_token_format(short_api_key, 'api_key')
        self.assertFalse(is_valid)
        self.assertIn("too short", message)

    def test_check_token_expiration(self):
        """Test token expiration checking"""
        current_time = int(time.time())
        
        # Expired token
        expired_time = current_time - 3600  # 1 hour ago
        is_valid, details = lambda_function.check_token_expiration(expired_time)
        self.assertFalse(is_valid)
        self.assertTrue(details['expired'])
        self.assertGreater(details['expired_seconds_ago'], 3500)
        
        # Valid token
        future_time = current_time + 3600  # 1 hour from now
        is_valid, details = lambda_function.check_token_expiration(future_time)
        self.assertTrue(is_valid)
        self.assertFalse(details['expired'])
        self.assertGreater(details['time_until_expiry_seconds'], 3500)

    def test_validate_jwt_claims(self):
        """Test JWT claims validation"""
        # Valid payload
        valid_payload = {
            'sub': 'user123',
            'exp': int(time.time()) + 3600,
            'iat': int(time.time()) - 60,
            'aud': 'test-audience',
            'iss': 'test-issuer'
        }
        
        is_valid, message = lambda_function.validate_jwt_claims(
            valid_payload,
            required_claims=['sub', 'exp', 'iat'],
            allowed_audiences=['test-audience'],
            allowed_issuers=['test-issuer']
        )
        self.assertTrue(is_valid)
        self.assertEqual(message, "All claims valid")
        
        # Missing required claim
        invalid_payload = {'exp': int(time.time()) + 3600, 'iat': int(time.time())}
        is_valid, message = lambda_function.validate_jwt_claims(invalid_payload)
        self.assertFalse(is_valid)
        self.assertIn("Missing required claim: sub", message)

    def test_sanitize_token_for_logging(self):
        """Test token sanitization for logging"""
        token = "abcdef1234567890"
        sanitized = lambda_function.sanitize_token_for_logging(token, 6)
        self.assertEqual(sanitized, "abcdef...**********")
        
        short_token = "abc"
        sanitized = lambda_function.sanitize_token_for_logging(short_token)
        self.assertEqual(sanitized, "***")

    def test_detect_suspicious_patterns(self):
        """Test suspicious pattern detection"""
        # SQL injection pattern
        suspicious_token = "'; DROP TABLE users; --"
        is_suspicious, indicators = lambda_function.detect_suspicious_patterns(suspicious_token)
        self.assertTrue(is_suspicious)
        self.assertIn('sql_injection_pattern', indicators)
        
        # Normal token
        normal_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9"
        is_suspicious, indicators = lambda_function.detect_suspicious_patterns(normal_token)
        self.assertFalse(is_suspicious)
        self.assertEqual(indicators, [])

    @patch('lambda_function.dynamodb')
    @patch('lambda_function.get_jwt_secret')
    def test_validate_jwt_token_success(self, mock_get_secret, mock_dynamodb):
        """Test successful JWT token validation"""
        mock_get_secret.return_value = self.test_jwt_secret
        
        # Create a valid JWT token
        payload = {
            'sub': 'user123',
            'exp': int(time.time()) + 3600,
            'iat': int(time.time()),
            'email': '<EMAIL>',
            'role': 'user'
        }
        token = jwt.encode(payload, self.test_jwt_secret, algorithm='HS256')
        
        with patch('lambda_function.log_auth_event'):
            principal_id, context = lambda_function.validate_jwt_token(token)
            
            self.assertEqual(principal_id, 'user123')
            self.assertEqual(context['email'], '<EMAIL>')
            self.assertEqual(context['token_type'], 'jwt')

    @patch('lambda_function.dynamodb')
    def test_validate_api_key_success(self, mock_dynamodb):
        """Test successful API key validation"""
        api_key = "test-api-key-1234567890abcdef"
        api_key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # Mock DynamoDB response
        mock_table = Mock()
        mock_dynamodb.Table.return_value = mock_table
        mock_table.get_item.return_value = {
            'Item': {
                'api_key_hash': api_key_hash,
                'is_active': True,
                'client_id': 'client123',
                'client_name': 'Test Client',
                'tier': 'premium',
                'permissions': ['read', 'write'],
                'rate_limit': 5000,
                'created_at': int(time.time()) - 86400,
                'expires_at': int(time.time()) + 86400
            }
        }
        
        with patch('lambda_function.log_auth_event'), \
             patch('lambda_function.check_rate_limit', return_value=True), \
             patch('lambda_function.update_last_used'):
            
            principal_id, context = lambda_function.validate_api_key(api_key)
            
            self.assertEqual(principal_id, 'client123')
            self.assertEqual(context['client_name'], 'Test Client')
            self.assertEqual(context['token_type'], 'api_key')

    def test_create_authorizer_response_allow(self):
        """Test authorizer response creation for allowed access"""
        context = {
            'user_id': 'user123',
            'permissions': ['read', 'write'],
            'rate_limit': 1000
        }
        
        response = lambda_function.create_authorizer_response(
            'user123', 'Allow', self.sample_event['methodArn'], context
        )
        
        self.assertEqual(response['principalId'], 'user123')
        self.assertEqual(response['policyDocument']['Statement'][0]['Effect'], 'Allow')
        self.assertIn('user_id', response['context'])
        self.assertIn('auth_timestamp', response['context'])

    def test_create_authorizer_response_deny(self):
        """Test authorizer response creation for denied access"""
        context = {
            'error_type': 'token_expired',
            'error_message': 'Token has expired'
        }
        
        response = lambda_function.create_authorizer_response(
            'unauthorized', 'Deny', self.sample_event['methodArn'], context
        )
        
        self.assertEqual(response['principalId'], 'unauthorized')
        self.assertEqual(response['policyDocument']['Statement'][0]['Effect'], 'Deny')
        self.assertEqual(response['context']['auth_failure_reason'], 'token_expired')
        self.assertEqual(response['context']['retry_guidance'], 'token_expired_refresh_required')


if __name__ == '__main__':
    unittest.main()
