"""
AWS Lambda Authorizer for API Gateway
Validates JWT tokens and tracks them in DynamoDB for revocation, refresh, and session management
"""

import json
import boto3
import logging
import jwt
import os
import time
import hashlib
from typing import Dict, Optional

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS DynamoDB
dynamodb = boto3.resource('dynamodb')

# Environment variables
JWT_TOKENS_TABLE = os.environ.get('JWT_TOKENS_TABLE', 'jwt_tokens')
JWT_SECRET = os.environ.get('JWT_SECRET', 'my-hardcoded-secret')  # set in Lambda env
TOKEN_CACHE_TTL = int(os.environ.get('TOKEN_CACHE_TTL', '300'))  # 5 minutes

# Caching
class TokenCache:
    def __init__(self):
        self.cache = {}

    def get(self, key):
        item = self.cache.get(key)
        if item and item['expires'] > time.time():
            return item['value']
        elif item:
            del self.cache[key]
        return None

    def set(self, key, value, ttl):
        self.cache[key] = {'value': value, 'expires': time.time() + ttl}

token_cache = TokenCache()

def lambda_handler(event, context):
    try:
        auth_token = extract_auth_token(event)
        method_arn = event['methodArn']

        if not auth_token or not auth_token.startswith('Bearer '):
            logger.warning("No valid JWT provided")
            raise Exception('Unauthorized')

        jwt_token = auth_token[7:]
        principal_id, context_data = validate_jwt_token(jwt_token)

        # Allow policy
        return create_authorizer_response(principal_id, 'Allow', method_arn, context_data)

    except Exception as e:
        logger.error(f"Authentication failed: {str(e)}")
        return create_authorizer_response('unauthorized', 'Deny', event['methodArn'])

def extract_auth_token(event: Dict) -> Optional[str]:
    # Check standard header
    if 'authorizationToken' in event:
        return event['authorizationToken']
    headers = event.get('headers', {})
    for k, v in headers.items():
        if k.lower() == 'authorization':
            return v
    return None

def validate_jwt_token(token: str) -> tuple[str, Dict]:
    try:
        # Cache check
        cache_key = hashlib.sha256(token.encode()).hexdigest()
        cached = token_cache.get(cache_key)
        if cached:
            return cached['principal_id'], cached['context']

        # Decode JWT
        payload = jwt.decode(
            token,
            JWT_SECRET,
            algorithms=['HS256'],
            options={'verify_exp': True, 'verify_iat': True}
        )

        token_id = payload.get('jti')
        user_id = payload.get('sub') or payload.get('user_id')
        if not user_id or not token_id:
            raise Exception("Missing sub/user_id or jti in token")

        # Lookup in DynamoDB
        table = dynamodb.Table(JWT_TOKENS_TABLE)
        resp = table.get_item(Key={'tokenId': token_id})
        item = resp.get('Item')

        if not item or item.get('status') != 'active':
            raise Exception("Token revoked or invalid")

        if int(item.get('expiresAt', 0)) < int(time.time()):
            raise Exception("Token expired")

        # Prepare context
        context_data = {
            'user_id': user_id,
            'email': payload.get('email', ''),
            'role': payload.get('role', 'user'),
            'permissions': payload.get('permissions', []),
            'token_type': 'jwt'
        }

        # Cache result
        token_cache.set(cache_key, {'principal_id': user_id, 'context': context_data}, TOKEN_CACHE_TTL)

        return user_id, context_data

    except jwt.ExpiredSignatureError:
        logger.warning("JWT expired")
        raise Exception("Expired")
    except Exception as e:
        logger.error(f"JWT validation failed: {str(e)}")
        raise Exception("Unauthorized")

def create_authorizer_response(principal_id: str, effect: str, method_arn: str, context: Dict = None):
    auth_response = {'principalId': principal_id}
    if effect and method_arn:
        auth_response['policyDocument'] = {
            'Version': '2012-10-17',
            'Statement': [{'Action': 'execute-api:Invoke', 'Effect': effect, 'Resource': method_arn}]
        }
    if context:
        auth_response['context'] = {}
        for k, v in context.items():
            auth_response['context'][k] = json.dumps(v) if isinstance(v, (dict, list)) else str(v)
    return auth_response
