# JWT Token Manager and Authorizer

A comprehensive AWS Lambda-based JWT token management system that handles authentication, authorization, token refresh, session management, and revocation with DynamoDB storage.

## 🚀 Features

### Core JWT Functionality
- **Token Creation**: Generate access and refresh JWT tokens with user data
- **Token Validation**: Secure token verification with session tracking
- **Token Refresh**: Seamless access token renewal using refresh tokens
- **Session Management**: Complete session lifecycle with DynamoDB storage
- **Token Revocation**: Secure logout and session termination
- **API Gateway Integration**: Native support for AWS API Gateway authorizer

### Security Features
- **Session-Based Tracking**: All tokens linked to DynamoDB sessions
- **JTI Validation**: Prevents token replay attacks
- **Automatic Expiration**: Configurable token and session TTL
- **Audit Logging**: Comprehensive security and access logging
- **Source Tracking**: IP address and user agent monitoring
- **Correlation IDs**: Request tracing across all operations

### Monitoring & Observability
- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Performance Metrics**: Processing time and success rate tracking
- **Security Events**: Automated threat detection and logging
- **Error Categorization**: Detailed error types for monitoring

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API Gateway   │────│  Lambda Function │────│    DynamoDB     │
│                 │    │                  │    │                 │
│ • Authentication│    │ • Token Creation │    │ • Session Store │
│ • Authorization │    │ • Token Refresh  │    │ • User Data     │
│ • Token Refresh │    │ • Session Mgmt   │    │ • Audit Trail   │
│ • Logout        │    │ • Validation     │    │ • TTL Cleanup   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 📋 API Endpoints

### 1. User Login
**Endpoint:** `POST /auth/login`

**Request:**
```json
{
  "username": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1Q...",
  "refresh_token": "eyJ0eXAiOiJKV1Q...",
  "token_type": "Bearer",
  "expires_in": 900,
  "session_id": "uuid-session-id",
  "user": {
    "user_id": "user-001",
    "email": "<EMAIL>",
    "role": "user"
  }
}
```

### 2. Token Refresh
**Endpoint:** `POST /auth/refresh`

**Request:**
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1Q..."
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1Q...",
  "token_type": "Bearer",
  "expires_in": 900
}
```

### 3. Logout
**Endpoint:** `POST /auth/logout`

**Request:**
```json
{
  "session_id": "uuid-session-id"
}
```

**Response:**
```json
{
  "message": "Logout successful",
  "session_id": "uuid-session-id"
}
```

### 4. API Gateway Authorization
**Header:** `Authorization: Bearer eyJ0eXAiOiJKV1Q...`

Returns AWS API Gateway policy allowing/denying access.

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `JWT_TOKENS_TABLE` | DynamoDB table name for sessions | `jwt_tokens` | Yes |
| `JWT_SECRET_KEY` | Secret key for JWT signing | - | Yes |
| `ACCESS_TOKEN_TTL` | Access token TTL in seconds | `900` (15 min) | No |
| `REFRESH_TOKEN_TTL` | Refresh token TTL in seconds | `604800` (7 days) | No |
| `TOKEN_CACHE_TTL` | Cache TTL in seconds | `300` (5 min) | No |
| `ENVIRONMENT` | Environment identifier | `dev` | No |

### DynamoDB Table Schema

**Table Name:** As specified in `JWT_TOKENS_TABLE`
**Primary Key:** `session_id` (String)

```json
{
  "session_id": "uuid-v4",
  "user_id": "string",
  "access_token_jti": "jwt-id-for-access-token",
  "refresh_token_jti": "jwt-id-for-refresh-token",
  "created_at": 1640995200,
  "last_used": 1640995200,
  "expires_at": 1641600000,
  "status": "active|revoked",
  "user_data": {
    "email": "<EMAIL>",
    "role": "user|admin",
    "permissions": ["read", "write"]
  },
  "source_ip": "***********",
  "user_agent": "Mozilla/5.0...",
  "ttl": 1641686400
}
```

## 🔧 Deployment

### 1. Infrastructure Setup

**Terraform Configuration:**
```hcl
# DynamoDB Table
resource "aws_dynamodb_table" "jwt_tokens" {
  name           = "jwt-tokens-${var.environment}"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "session_id"

  attribute {
    name = "session_id"
    type = "S"
  }

  ttl {
    attribute_name = "ttl"
    enabled        = true
  }

  tags = {
    Environment = var.environment
    Project     = "JWT-Auth"
  }
}

# Lambda Function
resource "aws_lambda_function" "jwt_authorizer" {
  filename      = "lambda-authorizer.zip"
  function_name = "jwt-authorizer-${var.environment}"
  role          = aws_iam_role.lambda_role.arn
  handler       = "lambda_function.lambda_handler"
  runtime       = "python3.9"
  timeout       = 30

  environment {
    variables = {
      JWT_TOKENS_TABLE    = aws_dynamodb_table.jwt_tokens.name
      JWT_SECRET_KEY      = var.jwt_secret_key
      ACCESS_TOKEN_TTL    = "900"
      REFRESH_TOKEN_TTL   = "604800"
      ENVIRONMENT         = var.environment
    }
  }
}
```

### 2. IAM Permissions

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "dynamodb:GetItem",
        "dynamodb:PutItem",
        "dynamodb:UpdateItem",
        "dynamodb:Query"
      ],
      "Resource": "arn:aws:dynamodb:*:*:table/jwt-tokens-*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:*:*:*"
    }
  ]
}
```

### 3. API Gateway Setup

```yaml
# API Gateway Configuration
Resources:
  AuthApi:
    Type: AWS::ApiGateway::RestApi
    Properties:
      Name: JWT-Auth-API
      
  LoginResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref AuthApi
      ParentId: !GetAtt AuthApi.RootResourceId
      PathPart: login
      
  LoginMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref AuthApi
      ResourceId: !Ref LoginResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub 
          - arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${LambdaArn}/invocations
          - LambdaArn: !GetAtt JWTAuthorizerFunction.Arn
```

## 🔐 Security Best Practices

### Token Security
- **Short-lived Access Tokens**: Default 15-minute expiration
- **Secure Refresh Tokens**: 7-day expiration with rotation
- **JTI Validation**: Prevents token replay attacks
- **Session Tracking**: All tokens linked to database sessions

### Session Management
- **Automatic Cleanup**: TTL-based session expiration
- **Revocation Support**: Immediate session termination
- **Audit Trail**: Complete session lifecycle logging
- **Source Tracking**: IP and user agent monitoring

### Monitoring
- **Correlation IDs**: Track requests across services
- **Security Events**: Automated threat detection
- **Performance Metrics**: Monitor authentication latency
- **Error Categorization**: Detailed failure analysis

## 📊 Monitoring & Logging

### CloudWatch Metrics
- Authentication success/failure rates
- Token refresh frequency
- Session duration analytics
- Error type distribution

### Log Structure
```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "correlation_id": "uuid-1234-5678",
  "message": "JWT token validation successful",
  "component": "lambda_authorizer",
  "version": "2.0",
  "user_id": "user-001",
  "session_id": "session-uuid",
  "processing_time_ms": 45.2
}
```

### Security Events
```json
{
  "event_type": "security",
  "security_event": "authentication_failure",
  "severity": "medium",
  "error_type": "invalid_credentials",
  "source_ip": "***********",
  "correlation_id": "uuid-1234-5678"
}
```

## 🧪 Testing

### Unit Tests
```bash
# Run comprehensive test suite
python -m pytest test_lambda_authorizer.py -v

# Test specific functionality
python -m pytest test_lambda_authorizer.py::TestJWTTokens::test_create_tokens -v
```

### Integration Testing
```bash
# Test login flow
curl -X POST https://api.example.com/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"<EMAIL>","password":"password123"}'

# Test token refresh
curl -X POST https://api.example.com/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{"refresh_token":"eyJ0eXAiOiJKV1Q..."}'

# Test protected endpoint
curl -X GET https://api.example.com/protected \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1Q..."
```

## 🚨 Troubleshooting

### Common Issues

1. **Token Validation Failures**
   - Check JWT secret key configuration
   - Verify token hasn't expired
   - Ensure session exists in DynamoDB

2. **Session Not Found**
   - Check DynamoDB table configuration
   - Verify TTL settings
   - Check session expiration

3. **High Latency**
   - Monitor DynamoDB throttling
   - Check cache hit rates
   - Review Lambda memory allocation

### Debug Commands
```bash
# Check DynamoDB sessions
aws dynamodb scan --table-name jwt-tokens-dev

# View Lambda logs
aws logs tail /aws/lambda/jwt-authorizer-dev --follow

# Test token locally
python -c "import jwt; print(jwt.decode('token', 'secret', algorithms=['HS256']))"
```

## 📚 Additional Resources

- [Security Guide](SECURITY_GUIDE.md) - Detailed security best practices
- [API Documentation](API_DOCS.md) - Complete API reference
- [Deployment Guide](DEPLOYMENT.md) - Step-by-step deployment instructions
- [Monitoring Guide](MONITORING.md) - CloudWatch setup and alerting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add comprehensive tests
4. Update documentation
5. Submit a pull request

## 🔄 Token Lifecycle

### Access Token Flow
```mermaid
sequenceDiagram
    participant Client
    participant API Gateway
    participant Lambda
    participant DynamoDB

    Client->>API Gateway: POST /auth/login
    API Gateway->>Lambda: Forward request
    Lambda->>Lambda: Validate credentials
    Lambda->>DynamoDB: Store session
    Lambda->>Client: Return tokens

    Client->>API Gateway: API request + Bearer token
    API Gateway->>Lambda: Authorize request
    Lambda->>DynamoDB: Validate session
    Lambda->>API Gateway: Allow/Deny policy
    API Gateway->>Client: API response
```

### Refresh Token Flow
```mermaid
sequenceDiagram
    participant Client
    participant Lambda
    participant DynamoDB

    Client->>Lambda: POST /auth/refresh + refresh_token
    Lambda->>Lambda: Validate refresh token
    Lambda->>DynamoDB: Get session data
    Lambda->>Lambda: Generate new access token
    Lambda->>DynamoDB: Update session with new JTI
    Lambda->>Client: Return new access token
```

## 🎯 Use Cases

### 1. Web Application Authentication
- Single-page applications (SPA)
- Mobile applications
- Server-side rendered applications

### 2. API Gateway Protection
- Microservices authentication
- Resource-based authorization
- Rate limiting per user

### 3. Session Management
- Multi-device login tracking
- Concurrent session limits
- Device-specific revocation

### 4. Audit and Compliance
- User activity tracking
- Security event monitoring
- Compliance reporting

## 🔧 Advanced Configuration

### Custom User Validation
Replace the demo user validation in `validate_user_credentials()`:

```python
def validate_user_credentials(username: str, password: str, correlation_id: str = None) -> Optional[Dict]:
    """
    Custom user validation - integrate with your user store
    """
    # Example: Database lookup
    user = database.get_user_by_email(username)
    if user and verify_password(password, user.password_hash):
        return {
            'user_id': user.id,
            'email': user.email,
            'role': user.role,
            'permissions': user.permissions
        }
    return None
```

### Custom Claims
Add custom claims to JWT tokens:

```python
# In create_jwt_tokens function
access_payload.update({
    'department': user_data.get('department'),
    'tenant_id': user_data.get('tenant_id'),
    'features': user_data.get('enabled_features', [])
})
```

### Rate Limiting
Implement per-user rate limiting:

```python
def check_rate_limit(user_id: str, correlation_id: str = None) -> bool:
    """Check if user is within rate limits"""
    # Implement Redis-based rate limiting
    # or DynamoDB with TTL
    pass
```

## 📈 Performance Optimization

### Caching Strategy
- **Token Cache**: 5-minute cache for validated tokens
- **Session Cache**: Cache frequently accessed sessions
- **User Data Cache**: Cache user permissions and roles

### DynamoDB Optimization
- **Provisioned Capacity**: For predictable workloads
- **Global Secondary Indexes**: For user-based queries
- **TTL Configuration**: Automatic cleanup of expired sessions

### Lambda Optimization
- **Memory Allocation**: 512MB recommended for optimal performance
- **Timeout Configuration**: 30 seconds for complex operations
- **Cold Start Mitigation**: Keep functions warm with scheduled events

## 🔍 Monitoring Queries

### CloudWatch Insights Queries

**Authentication Success Rate:**
```sql
fields @timestamp, success, auth_type
| filter @message like /METRIC:/
| stats count() by success, auth_type
| sort @timestamp desc
```

**Error Analysis:**
```sql
fields @timestamp, error_type, message
| filter error_type exists
| stats count() by error_type
| sort count desc
```

**Performance Monitoring:**
```sql
fields @timestamp, processing_time_ms
| filter processing_time_ms > 0
| stats avg(processing_time_ms), max(processing_time_ms), min(processing_time_ms) by bin(5m)
```

**Security Events:**
```sql
fields @timestamp, security_event, source_ip
| filter @message like /SECURITY:/
| stats count() by security_event, source_ip
| sort count desc
```

## 🚀 Deployment Checklist

### Pre-Deployment
- [ ] Configure environment variables
- [ ] Set up DynamoDB table with TTL
- [ ] Configure IAM roles and policies
- [ ] Set up CloudWatch log groups
- [ ] Configure API Gateway endpoints

### Post-Deployment
- [ ] Test all authentication flows
- [ ] Verify token validation works
- [ ] Check session management
- [ ] Monitor CloudWatch metrics
- [ ] Set up alerting rules

### Production Readiness
- [ ] Load testing completed
- [ ] Security review passed
- [ ] Monitoring dashboards configured
- [ ] Backup and recovery tested
- [ ] Documentation updated

## 📞 Support

For issues and questions:
1. Check the [troubleshooting section](#-troubleshooting)
2. Review CloudWatch logs with correlation IDs
3. Consult the [Security Guide](SECURITY_GUIDE.md)
4. Run the test suite for validation
5. Create an issue in the repository

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
