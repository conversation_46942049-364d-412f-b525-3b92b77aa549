{"version": 4, "terraform_version": "1.5.3", "serial": 20, "lineage": "0169c89d-1d3d-ef83-7ae4-1ba24df90b56", "outputs": {"jwt_secret_arn": {"value": "arn:aws:secretsmanager:us-east-1:************:secret:notification-platform/jwt-secrets-dev-5s3EhT", "type": "string", "sensitive": true}}, "resources": [{"mode": "managed", "type": "aws_api_gateway_authorizer", "name": "lambda", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:apigateway:us-east-1::/restapis/qfocgfox34/authorizers/ggr2mp", "authorizer_credentials": "", "authorizer_result_ttl_in_seconds": 300, "authorizer_uri": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:************:function:YANTECH-YNP01-AWS-Lambda-Authorizer-dev/invocations", "id": "ggr2mp", "identity_source": "method.request.header.Authorization", "identity_validation_expression": "", "name": "YANTECH-YNP01-AWS-APIGateway-Authorizer-dev", "provider_arns": [], "region": "us-east-1", "rest_api_id": "qfocgfox34", "type": "TOKEN"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.main", "aws_dynamodb_table.api_keys", "aws_iam_role.lambda_authorizer", "aws_lambda_function.authorizer", "aws_secretsmanager_secret.jwt_secret"]}]}, {"mode": "managed", "type": "aws_api_gateway_deployment", "name": "test", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"created_date": "2025-08-30T09:26:15Z", "description": "", "id": "78g9ry", "region": "us-east-1", "rest_api_id": "qfocgfox34", "triggers": null, "variables": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda", "aws_api_gateway_integration.test", "aws_api_gateway_method.test", "aws_api_gateway_resource.test", "aws_api_gateway_rest_api.main"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "test", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": null, "cache_namespace": "6kffi9", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "GET", "id": "agi-qfocgfox34-6kffi9-GET", "integration_http_method": "", "passthrough_behavior": "WHEN_NO_MATCH", "region": "us-east-1", "request_parameters": null, "request_templates": {"application/json": "{\"statusCode\":200}"}, "resource_id": "6kffi9", "rest_api_id": "qfocgfox34", "timeout_milliseconds": 29000, "tls_config": [], "type": "MOCK", "uri": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda", "aws_api_gateway_method.test", "aws_api_gateway_resource.test", "aws_api_gateway_rest_api.main"]}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "test", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "CUSTOM", "authorization_scopes": null, "authorizer_id": "ggr2mp", "http_method": "GET", "id": "agm-qfocgfox34-6kffi9-GET", "operation_name": null, "region": "us-east-1", "request_models": null, "request_parameters": null, "request_validator_id": null, "resource_id": "6kffi9", "rest_api_id": "qfocgfox34"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda", "aws_api_gateway_resource.test", "aws_api_gateway_rest_api.main"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "test", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "6kffi9", "parent_id": "tumpfnmss1", "path": "/test", "path_part": "test", "region": "us-east-1", "rest_api_id": "qfocgfox34"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.main"]}]}, {"mode": "managed", "type": "aws_api_gateway_rest_api", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_source": "HEADER", "arn": "arn:aws:apigateway:us-east-1::/restapis/qfocgfox34", "binary_media_types": [], "body": null, "created_date": "2025-08-30T08:29:46Z", "description": "Notification Platform API Gateway", "disable_execute_api_endpoint": false, "endpoint_configuration": [{"ip_address_type": "ipv4", "types": ["EDGE"], "vpc_endpoint_ids": []}], "execution_arn": "arn:aws:execute-api:us-east-1:************:qfocgfox34", "fail_on_warnings": null, "id": "qfocgfox34", "minimum_compression_size": "", "name": "YANTECH-YNP01-AWS-APIGateway-dev", "parameters": null, "policy": "", "put_rest_api_mode": null, "region": "us-east-1", "root_resource_id": "tumpfnmss1", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_api_gateway_stage", "name": "test", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_log_settings": [], "arn": "arn:aws:apigateway:us-east-1::/restapis/qfocgfox34/stages/test", "cache_cluster_enabled": false, "cache_cluster_size": "", "canary_settings": [], "client_certificate_id": "", "deployment_id": "78g9ry", "description": "", "documentation_version": "", "execution_arn": "arn:aws:execute-api:us-east-1:************:qfocgfox34/test", "id": "ags-qfocgfox34-test", "invoke_url": "https://qfocgfox34.execute-api.us-east-1.amazonaws.com/test", "region": "us-east-1", "rest_api_id": "qfocgfox34", "stage_name": "test", "tags": null, "tags_all": {}, "variables": null, "web_acl_arn": "", "xray_tracing_enabled": false}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda", "aws_api_gateway_deployment.test", "aws_api_gateway_integration.test", "aws_api_gateway_method.test", "aws_api_gateway_resource.test", "aws_api_gateway_rest_api.main"]}]}, {"mode": "managed", "type": "aws_dynamodb_table", "name": "api_keys", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:dynamodb:us-east-1:************:table/YANTECH-YNP01-AWS-DynamoDB-APIKeys-dev", "attribute": [{"name": "api_key_hash", "type": "S"}], "billing_mode": "PAY_PER_REQUEST", "deletion_protection_enabled": false, "global_secondary_index": [], "hash_key": "api_key_hash", "id": "YANTECH-YNP01-AWS-DynamoDB-APIKeys-dev", "import_table": [], "local_secondary_index": [], "name": "YANTECH-YNP01-AWS-DynamoDB-APIKeys-dev", "on_demand_throughput": [], "point_in_time_recovery": [{"enabled": false, "recovery_period_in_days": 0}], "range_key": null, "read_capacity": 0, "region": "us-east-1", "replica": [], "restore_date_time": null, "restore_source_name": null, "restore_source_table_arn": null, "restore_to_latest_time": null, "server_side_encryption": [], "stream_arn": "", "stream_enabled": false, "stream_label": "", "stream_view_type": "", "table_class": "STANDARD", "tags": {"Environment": "dev", "Project": "YNP01"}, "tags_all": {"Environment": "dev", "Project": "YNP01"}, "timeouts": null, "ttl": [{"attribute_name": "", "enabled": false}], "write_capacity": 0}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjYwMDAwMDAwMDAwMCwidXBkYXRlIjozNjAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0="}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "lambda_authorizer", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/YANTECH-YNP01-AWS-Lambda-AuthorizerPolicy-dev", "attachment_count": 1, "description": "Permissions for Lambda Authorizer", "id": "arn:aws:iam::************:policy/YANTECH-YNP01-AWS-Lambda-AuthorizerPolicy-dev", "name": "YANTECH-YNP01-AWS-Lambda-AuthorizerPolicy-dev", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:PutLogEvents\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:*:*:*\"},{\"Action\":\"secretsmanager:GetSecretValue\",\"Effect\":\"Allow\",\"Resource\":\"arn:aws:secretsmanager:us-east-1:************:secret:notification-platform/jwt-secrets-dev-5s3EhT\"},{\"Action\":[\"dynamodb:GetItem\",\"dynamodb:UpdateItem\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:dynamodb:us-east-1:************:table/YANTECH-YNP01-AWS-DynamoDB-APIKeys-dev\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPA6GGOYOFHN7E6GKGBJ", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_dynamodb_table.api_keys", "aws_secretsmanager_secret.jwt_secret"]}]}, {"mode": "managed", "type": "aws_iam_role", "name": "lambda_authorizer", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/YANTECH-YNP01-AWS-Lambda-AuthorizerRole-dev", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-08-30T08:29:46Z", "description": "", "force_detach_policies": false, "id": "YANTECH-YNP01-AWS-Lambda-AuthorizerRole-dev", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::************:policy/YANTECH-YNP01-AWS-Lambda-AuthorizerPolicy-dev"], "max_session_duration": 3600, "name": "YANTECH-YNP01-AWS-Lambda-AuthorizerRole-dev", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROA6GGOYOFHOIW56GAAF"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "lambda_authorizer", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "YANTECH-YNP01-AWS-Lambda-AuthorizerRole-dev/arn:aws:iam::************:policy/YANTECH-YNP01-AWS-Lambda-AuthorizerPolicy-dev", "policy_arn": "arn:aws:iam::************:policy/YANTECH-YNP01-AWS-Lambda-AuthorizerPolicy-dev", "role": "YANTECH-YNP01-AWS-Lambda-AuthorizerRole-dev"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_dynamodb_table.api_keys", "aws_iam_policy.lambda_authorizer", "aws_iam_role.lambda_authorizer", "aws_secretsmanager_secret.jwt_secret"]}]}, {"mode": "managed", "type": "aws_lambda_function", "name": "authorizer", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architectures": ["x86_64"], "arn": "arn:aws:lambda:us-east-1:************:function:YANTECH-YNP01-AWS-Lambda-Authorizer-dev", "code_sha256": "pmO7SYJlRx8Y3VLJjqUiB1uO9sJYzfnJpefEgpRYdU4=", "code_signing_config_arn": "", "dead_letter_config": [], "description": "", "environment": [{"variables": {"API_KEYS_TABLE": "YANTECH-YNP01-AWS-DynamoDB-APIKeys-dev", "ENVIRONMENT": "dev", "JWT_SECRET_NAME": "notification-platform/jwt-secrets-dev", "TOKEN_CACHE_TTL": "300"}}], "ephemeral_storage": [{"size": 512}], "file_system_config": [], "filename": "lambda-authorizer.zip", "function_name": "YANTECH-YNP01-AWS-Lambda-Authorizer-dev", "handler": "lambda_function.lambda_handler", "id": "YANTECH-YNP01-AWS-Lambda-Authorizer-dev", "image_config": [], "image_uri": "", "invoke_arn": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:************:function:YANTECH-YNP01-AWS-Lambda-Authorizer-dev/invocations", "kms_key_arn": "", "last_modified": "2025-08-30T08:34:56.226+0000", "layers": [], "logging_config": [{"application_log_level": "", "log_format": "Text", "log_group": "/aws/lambda/YANTECH-YNP01-AWS-Lambda-Authorizer-dev", "system_log_level": ""}], "memory_size": 128, "package_type": "Zip", "publish": false, "qualified_arn": "arn:aws:lambda:us-east-1:************:function:YANTECH-YNP01-AWS-Lambda-Authorizer-dev:$LATEST", "qualified_invoke_arn": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:************:function:YANTECH-YNP01-AWS-Lambda-Authorizer-dev:$LATEST/invocations", "region": "us-east-1", "replace_security_groups_on_destroy": null, "replacement_security_group_ids": null, "reserved_concurrent_executions": -1, "role": "arn:aws:iam::************:role/YANTECH-YNP01-AWS-Lambda-AuthorizerRole-dev", "runtime": "python3.9", "s3_bucket": null, "s3_key": null, "s3_object_version": null, "signing_job_arn": "", "signing_profile_version_arn": "", "skip_destroy": false, "snap_start": [], "source_code_hash": "", "source_code_size": 340982, "tags": {}, "tags_all": {}, "timeout": 10, "timeouts": null, "tracing_config": [{"mode": "PassThrough"}], "version": "$LATEST", "vpc_config": []}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["aws_dynamodb_table.api_keys", "aws_iam_role.lambda_authorizer", "aws_secretsmanager_secret.jwt_secret"]}]}, {"mode": "managed", "type": "aws_lambda_permission", "name": "apigw", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "YANTECH-YNP01-AWS-Lambda-Authorizer-dev", "function_url_auth_type": null, "id": "AllowAPIGatewayInvoke", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "region": "us-east-1", "source_account": null, "source_arn": "arn:aws:execute-api:us-east-1:************:qfocgfox34/authorizers/ggr2mp", "statement_id": "AllowAPIGatewayInvoke", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda", "aws_api_gateway_rest_api.main", "aws_dynamodb_table.api_keys", "aws_iam_role.lambda_authorizer", "aws_lambda_function.authorizer", "aws_secretsmanager_secret.jwt_secret"]}]}, {"mode": "managed", "type": "aws_secretsmanager_secret", "name": "jwt_secret", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:us-east-1:************:secret:notification-platform/jwt-secrets-dev-5s3EhT", "description": "JWT signing secret for notification platform", "force_overwrite_replica_secret": false, "id": "arn:aws:secretsmanager:us-east-1:************:secret:notification-platform/jwt-secrets-dev-5s3EhT", "kms_key_id": "", "name": "notification-platform/jwt-secrets-dev", "name_prefix": "", "policy": "", "recovery_window_in_days": 30, "region": "us-east-1", "replica": [], "tags": {"Environment": "dev", "Project": "YNP01"}, "tags_all": {"Environment": "dev", "Project": "YNP01"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_secretsmanager_secret_version", "name": "jwt_secret", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:us-east-1:************:secret:notification-platform/jwt-secrets-dev-5s3EhT", "has_secret_string_wo": null, "id": "arn:aws:secretsmanager:us-east-1:************:secret:notification-platform/jwt-secrets-dev-5s3EhT|terraform-20250830083453437600000002", "region": "us-east-1", "secret_binary": "", "secret_id": "arn:aws:secretsmanager:us-east-1:************:secret:notification-platform/jwt-secrets-dev-5s3EhT", "secret_string": "{\"jwt_secret\":\"doWSTmq[g\\u0026*X68-Z^RQ9Yi^3.t+Zn:iDv^~u,8l-PcaQ;[BXq{5Jgv_kII2p\\u0026KG1\"}", "secret_string_wo": null, "secret_string_wo_version": null, "version_id": "terraform-20250830083453437600000002", "version_stages": ["AWSCURRENT"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_string"}]], "private": "bnVsbA==", "dependencies": ["aws_secretsmanager_secret.jwt_secret", "random_password.jwt_secret"]}]}, {"mode": "managed", "type": "random_password", "name": "jwt_secret", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 3, "attributes": {"bcrypt_hash": "$2a$10$vls5PIsF8S5JaXKVWiPE6OmwwJQcwKJkXXxF0/Qj4uq2H8LjdoME2", "id": "none", "keepers": null, "length": 64, "lower": true, "min_lower": 0, "min_numeric": 0, "min_special": 0, "min_upper": 0, "number": true, "numeric": true, "override_special": "!@#$%^&*()_-+={}[]|:;<>,.?/~", "result": "doWSTmq[g&*X68-Z^RQ9Yi^3.t+Zn:iDv^~u,8l-PcaQ;[BXq{5Jgv_kII2p&KG1", "special": true, "upper": true}, "sensitive_attributes": []}]}], "check_results": null}