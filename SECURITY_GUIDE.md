# Lambda Authorizer Security Guide

## Overview

This document outlines the security enhancements implemented in the Lambda Authorizer and provides best practices for secure authentication and authorization.

## Enhanced Security Features

### 1. Token Expiration Checks

#### JWT Token Expiration
- **Comprehensive Validation**: Validates `exp`, `iat`, and `nbf` claims
- **Grace Period Support**: Optional grace period for clock skew tolerance
- **Expiration Warnings**: Logs warnings for tokens expiring soon (within 5 minutes)
- **Detailed Logging**: Provides precise expiration timing information

```python
# Example: JWT with proper expiration handling
payload = {
    'sub': 'user123',
    'exp': int(time.time()) + 3600,  # Expires in 1 hour
    'iat': int(time.time()),         # Issued now
    'nbf': int(time.time()) - 60     # Valid from 1 minute ago
}
```

#### API Key Expiration
- **Database-Driven Expiration**: Checks `expires_at` field in DynamoDB
- **Expiration Warnings**: Warns when API keys expire within 7 days
- **Detailed Timing**: Provides exact expiration timestamps and remaining time
- **Graceful Handling**: Clear error messages for expired keys

### 2. Detailed Authentication Logging

#### Structured Logging Format
All authentication events use structured JSON logging with:
- **Correlation IDs**: Unique identifiers for request tracing
- **Timestamps**: ISO 8601 formatted timestamps with timezone
- **Error Categories**: Categorized error types for monitoring
- **Security Context**: IP addresses, user agents, and request metadata

#### Log Levels and Types
- **INFO**: Successful authentications, cache hits, normal operations
- **WARNING**: Expiring tokens, suspicious patterns, authentication failures
- **ERROR**: Service errors, unexpected failures, security violations

#### Metrics Logging
Dedicated metrics logging for:
- Authentication success/failure rates
- Processing times
- Error type distributions
- Token type usage patterns

### 3. Robust Error Handling

#### Custom Error Types
Specific error codes for different failure scenarios:
- `MISSING_TOKEN`: No authentication token provided
- `INVALID_FORMAT`: Malformed token structure
- `TOKEN_EXPIRED`: Expired JWT or API key
- `TOKEN_REVOKED`: Revoked or inactive token
- `INVALID_SIGNATURE`: Invalid JWT signature
- `API_KEY_NOT_FOUND`: API key not in database
- `RATE_LIMIT_EXCEEDED`: Rate limit violations
- `SERVICE_UNAVAILABLE`: Infrastructure errors

#### Enhanced Response Context
Authorizer responses include:
- **Error Details**: Specific error types and messages
- **Retry Guidance**: Actionable guidance for different error types
- **Security Headers**: Additional context for successful authentications
- **Correlation IDs**: Request tracking identifiers

## Security Best Practices

### 1. Token Management

#### JWT Tokens
- **Short Expiration**: Use short-lived tokens (15-60 minutes)
- **Refresh Strategy**: Implement refresh token rotation
- **Secure Storage**: Store JWT secrets in AWS Secrets Manager
- **Algorithm Validation**: Only allow expected algorithms (HS256, RS256)

#### API Keys
- **Rotation Policy**: Regular API key rotation (90 days recommended)
- **Scope Limitation**: Assign minimal required permissions
- **Expiration Dates**: Set explicit expiration dates
- **Secure Generation**: Use cryptographically secure random generation

### 2. Monitoring and Alerting

#### Key Metrics to Monitor
- Authentication failure rates (>5% may indicate attacks)
- Token expiration patterns
- Suspicious IP addresses or user agents
- Rate limit violations
- Service error rates

#### Recommended Alerts
- High authentication failure rates
- Repeated failures from same IP
- Service availability issues
- Unusual token patterns

### 3. Rate Limiting

#### Implementation Strategies
- **API Gateway Throttling**: Primary rate limiting at gateway level
- **Token-Based Limits**: Per-token rate limits stored in DynamoDB
- **IP-Based Limits**: Additional IP-based rate limiting
- **Distributed Caching**: Use Redis for distributed rate limiting

### 4. Security Validations

#### Token Format Validation
- JWT structure validation (3 base64-encoded parts)
- API key format validation (length, character set)
- Input sanitization for logging

#### Suspicious Pattern Detection
- SQL injection patterns in tokens
- XSS patterns in authentication headers
- Oversized tokens (potential buffer overflow)
- Low entropy tokens (potential fuzzing)

## Configuration Guidelines

### Environment Variables
```bash
# Required
API_KEYS_TABLE=your-api-keys-table
JWT_SECRET_NAME=your-jwt-secret-name
ENVIRONMENT=dev|staging|prod

# Optional
TOKEN_CACHE_TTL=300
LOG_LEVEL=INFO
RATE_LIMIT_WINDOW=3600
```

### DynamoDB Schema
```json
{
  "api_key_hash": "string (primary key)",
  "client_id": "string",
  "client_name": "string",
  "is_active": "boolean",
  "expires_at": "number (unix timestamp)",
  "created_at": "number (unix timestamp)",
  "last_used": "number (unix timestamp)",
  "rate_limit": "number",
  "permissions": "list",
  "tier": "string"
}
```

### JWT Secret Format
```json
{
  "jwt_secret": "your-256-bit-secret-key"
}
```

## Incident Response

### Authentication Failures
1. **Identify Pattern**: Check correlation IDs and error types
2. **Source Analysis**: Examine IP addresses and user agents
3. **Impact Assessment**: Determine scope of affected users/systems
4. **Mitigation**: Implement temporary blocks if necessary

### Token Compromise
1. **Immediate Revocation**: Revoke compromised tokens
2. **Audit Trail**: Review access logs for unauthorized usage
3. **Notification**: Inform affected users/systems
4. **Key Rotation**: Rotate signing keys if necessary

### Service Degradation
1. **Health Checks**: Verify AWS service availability
2. **Cache Clearing**: Clear potentially corrupted caches
3. **Fallback Procedures**: Implement emergency access procedures
4. **Monitoring**: Increase monitoring frequency during recovery

## Testing and Validation

### Unit Tests
Run comprehensive tests covering:
- Token format validation
- Expiration checking
- Error handling scenarios
- Logging functionality

```bash
python -m pytest test_lambda_authorizer.py -v
```

### Integration Tests
- End-to-end authentication flows
- AWS service integration
- Performance under load
- Security vulnerability scanning

### Security Testing
- Penetration testing of authentication endpoints
- Token manipulation testing
- Rate limiting validation
- Input validation testing

## Compliance Considerations

### Data Protection
- **PII Handling**: Avoid logging sensitive personal information
- **Token Sanitization**: Sanitize tokens in logs
- **Retention Policies**: Implement log retention policies
- **Encryption**: Encrypt sensitive data at rest and in transit

### Audit Requirements
- **Access Logging**: Comprehensive access logs
- **Change Tracking**: Track configuration changes
- **Compliance Reporting**: Generate compliance reports
- **Data Lineage**: Track data flow and transformations

## Performance Optimization

### Caching Strategy
- **Token Validation**: Cache valid tokens for 5 minutes
- **JWT Secrets**: Cache secrets for 1 hour
- **API Key Data**: Cache API key metadata
- **Negative Caching**: Cache failed lookups briefly

### Resource Management
- **Connection Pooling**: Reuse database connections
- **Memory Management**: Monitor Lambda memory usage
- **Cold Start Optimization**: Minimize initialization time
- **Timeout Configuration**: Set appropriate timeouts

## Troubleshooting

### Common Issues
1. **High Latency**: Check DynamoDB throttling and cache hit rates
2. **Authentication Failures**: Verify token formats and expiration
3. **Service Errors**: Check AWS service health and permissions
4. **Cache Issues**: Monitor cache hit rates and TTL settings

### Debug Tools
- CloudWatch Logs for detailed error analysis
- X-Ray tracing for performance analysis
- Correlation IDs for request tracking
- Structured logging for automated analysis
