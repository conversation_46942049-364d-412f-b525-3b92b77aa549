# Lambda Authorizer Enhancements

## Overview

This document describes the comprehensive enhancements made to the Lambda Authorizer to improve security, logging, and error handling capabilities.

## What's New

### 🔒 Enhanced Token Expiration Checks
- **Comprehensive JWT Validation**: Added validation for `exp`, `iat`, `nbf`, and other standard claims
- **API Key Expiration**: Implemented database-driven expiration checking with detailed timing
- **Grace Period Support**: Optional grace periods for clock skew tolerance
- **Expiration Warnings**: Proactive warnings for tokens nearing expiration

### 📊 Detailed Authentication Logging
- **Structured JSON Logging**: All events logged in structured format for easy parsing
- **Correlation IDs**: Unique request tracking across all log entries
- **Metrics Logging**: Dedicated metrics for monitoring authentication patterns
- **Security Event Logging**: Special logging for security-related events

### 🛡️ Robust Error Handling
- **Custom Error Types**: Specific error codes for different failure scenarios
- **Enhanced Response Context**: Detailed error information in authorizer responses
- **Retry Guidance**: Actionable guidance for different error types
- **Security Headers**: Additional context for successful authentications

### 🔧 Token Validation Utilities
- **Format Validation**: Comprehensive token format checking
- **Suspicious Pattern Detection**: Automated detection of potential attacks
- **Claims Validation**: Configurable JWT claims validation
- **Security Sanitization**: Safe token handling for logging

## Implementation Details

### New Error Types
```python
class AuthError(Enum):
    MISSING_TOKEN = "missing_token"
    INVALID_FORMAT = "invalid_format"
    TOKEN_EXPIRED = "token_expired"
    TOKEN_REVOKED = "token_revoked"
    INVALID_SIGNATURE = "invalid_signature"
    API_KEY_NOT_FOUND = "api_key_not_found"
    API_KEY_INACTIVE = "api_key_inactive"
    API_KEY_EXPIRED = "api_key_expired"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    SERVICE_UNAVAILABLE = "service_unavailable"
    VALIDATION_FAILED = "validation_failed"
```

### Enhanced Logging Structure
```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "correlation_id": "uuid-1234-5678-9012",
  "message": "Authentication successful",
  "component": "lambda_authorizer",
  "version": "2.0",
  "error_type": "token_expired",
  "error_category": "token_error",
  "principal_id": "user123",
  "processing_time_ms": 45.2
}
```

### Token Validation Features
- **JWT Format Validation**: Ensures proper 3-part structure
- **API Key Format Validation**: Validates length and character set
- **Expiration Checking**: Precise expiration validation with detailed timing
- **Claims Validation**: Configurable validation of JWT claims
- **Suspicious Pattern Detection**: Automated security threat detection

## Usage Examples

### JWT Token Validation
```python
# The enhanced validator now provides detailed expiration information
try:
    principal_id, context = validate_jwt_token(jwt_token)
    print(f"Authentication successful for {principal_id}")
except AuthException as e:
    print(f"Authentication failed: {e.error_type.value} - {e.message}")
```

### API Key Validation
```python
# Enhanced API key validation with expiration warnings
try:
    principal_id, context = validate_api_key(api_key)
    if context.get('expires_soon'):
        print("Warning: API key expires soon")
except AuthException as e:
    print(f"API key validation failed: {e.error_type.value}")
```

### Custom Error Handling
```python
# Specific error handling for different scenarios
try:
    # Authentication logic
    pass
except AuthException as e:
    if e.error_type == AuthError.TOKEN_EXPIRED:
        # Handle expired token
        return redirect_to_refresh()
    elif e.error_type == AuthError.RATE_LIMIT_EXCEEDED:
        # Handle rate limiting
        return rate_limit_response()
```

## Configuration

### Required Environment Variables
```bash
API_KEYS_TABLE=your-dynamodb-table-name
JWT_SECRET_NAME=your-secrets-manager-secret-name
ENVIRONMENT=dev|staging|prod
```

### Optional Environment Variables
```bash
TOKEN_CACHE_TTL=300          # Cache TTL in seconds
LOG_LEVEL=INFO               # Logging level
RATE_LIMIT_WINDOW=3600       # Rate limit window in seconds
```

### DynamoDB Table Schema
The API keys table should include these fields:
- `api_key_hash` (String, Primary Key)
- `client_id` (String)
- `is_active` (Boolean)
- `expires_at` (Number, Unix timestamp)
- `created_at` (Number, Unix timestamp)
- `last_used` (Number, Unix timestamp)
- `rate_limit` (Number)
- `permissions` (List)

## Testing

### Running Tests
```bash
# Install test dependencies
pip install pytest pytest-mock

# Run all tests
python -m pytest test_lambda_authorizer.py -v

# Run specific test categories
python -m pytest test_lambda_authorizer.py::TestLambdaAuthorizer::test_validate_jwt_token_success -v
```

### Test Coverage
The test suite covers:
- ✅ Token format validation
- ✅ Expiration checking
- ✅ Error handling scenarios
- ✅ Logging functionality
- ✅ Security pattern detection
- ✅ Response generation

## Monitoring and Alerting

### Key Metrics to Monitor
1. **Authentication Success Rate**: Should be >95%
2. **Average Processing Time**: Should be <100ms
3. **Error Distribution**: Monitor error type patterns
4. **Token Expiration Patterns**: Track expiration warnings

### CloudWatch Queries
```sql
-- Authentication failure rate
fields @timestamp, message
| filter message like /Authentication failed/
| stats count() by bin(5m)

-- Processing time analysis
fields @timestamp, processing_time_ms
| filter processing_time_ms > 0
| stats avg(processing_time_ms), max(processing_time_ms) by bin(5m)

-- Error type distribution
fields @timestamp, error_type
| filter error_type exists
| stats count() by error_type
```

## Security Considerations

### Token Security
- JWT secrets stored in AWS Secrets Manager
- API key hashes stored (never plain text)
- Token sanitization in logs
- Correlation IDs for audit trails

### Rate Limiting
- Per-token rate limits
- IP-based rate limiting (future enhancement)
- Distributed rate limiting with Redis (recommended)

### Monitoring
- Failed authentication alerts
- Suspicious pattern detection
- Service health monitoring
- Performance degradation alerts

## Migration Guide

### From Previous Version
1. **Update Lambda Function**: Deploy the enhanced lambda_function.py
2. **Update IAM Permissions**: Ensure Secrets Manager access
3. **Update Environment Variables**: Add new configuration options
4. **Test Thoroughly**: Run comprehensive tests before production deployment

### Backward Compatibility
- All existing API key formats supported
- JWT validation remains compatible
- Response format enhanced but backward compatible
- Existing DynamoDB schema supported (new fields optional)

## Performance Impact

### Improvements
- ✅ Better caching strategy reduces DynamoDB calls
- ✅ Structured logging improves debugging efficiency
- ✅ Early validation reduces processing time for invalid tokens

### Considerations
- Slightly increased memory usage due to enhanced logging
- Additional validation steps add ~5-10ms processing time
- Correlation ID generation adds minimal overhead

## Troubleshooting

### Common Issues
1. **High Processing Times**: Check DynamoDB throttling and cache hit rates
2. **Authentication Failures**: Verify token formats and expiration settings
3. **Missing Logs**: Ensure proper log level configuration
4. **Cache Issues**: Monitor cache TTL settings and hit rates

### Debug Steps
1. Check correlation IDs in CloudWatch Logs
2. Verify environment variable configuration
3. Test token validation independently
4. Monitor AWS service health

## Future Enhancements

### Planned Features
- [ ] Redis-based distributed rate limiting
- [ ] Advanced threat detection
- [ ] Token refresh automation
- [ ] Multi-region support
- [ ] Enhanced audit reporting

### Contribution Guidelines
1. Follow existing code style and patterns
2. Add comprehensive tests for new features
3. Update documentation for any changes
4. Ensure backward compatibility
5. Test thoroughly in staging environment

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review CloudWatch Logs with correlation IDs
3. Consult the Security Guide for best practices
4. Test with the provided test suite
