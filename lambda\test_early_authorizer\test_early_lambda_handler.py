"""
Unit tests for lambda_handler in lambda/authorizer.py

Covers happy paths and edge cases using pytest.
"""

import pytest
import json
import time

from unittest.mock import patch, MagicMock

# Import the function to test
from authorizer import lambda_handler

@pytest.mark.usefixtures("mock_env")
class TestLambdaHandler:
    # --- Fixtures ---
    @pytest.fixture(autouse=True)
    def mock_env(self, monkeypatch):
        # Patch environment variables
        monkeypatch.setenv('JWT_TOKENS_TABLE', 'jwt_tokens')
        monkeypatch.setenv('JWT_SECRET', 'test-secret')
        monkeypatch.setenv('TOKEN_CACHE_TTL', '300')

    @pytest.fixture
    def valid_jwt_payload(self):
        # Minimal valid JWT payload
        return {
            'jti': 'token123',
            'sub': 'user456',
            'email': '<EMAIL>',
            'role': 'admin',
            'permissions': ['read', 'write'],
            'exp': int(time.time()) + 600,
            'iat': int(time.time())
        }

    @pytest.fixture
    def valid_jwt_token(self, valid_jwt_payload):
        # Create a valid JWT token using PyJWT
        import jwt
        return jwt.encode(valid_jwt_payload, 'test-secret', algorithm='HS256')

    @pytest.fixture
    def method_arn(self):
        return "arn:aws:execute-api:us-east-1:123456789012:example/prod/GET/resource"

    # --- Happy Path Tests ---
    @pytest.mark.happy_path
    def test_valid_authorization_token_header(self, valid_jwt_token, method_arn, valid_jwt_payload):
        """
        Test: lambda_handler returns Allow policy for valid JWT in 'authorizationToken' field.
        """
        event = {
            'authorizationToken': f'Bearer {valid_jwt_token}',
            'methodArn': method_arn
        }

        # Mock DynamoDB and token_cache
        with patch('authorizer.dynamodb') as mock_dynamodb, \
             patch('authorizer.token_cache') as mock_cache, \
             patch('authorizer.jwt.decode') as mock_jwt_decode:

            # Mock JWT decode
            mock_jwt_decode.return_value = valid_jwt_payload

            # Mock DynamoDB table
            mock_table = MagicMock()
            mock_table.get_item.return_value = {
                'Item': {
                    'tokenId': valid_jwt_payload['jti'],
                    'status': 'active',
                    'expiresAt': int(time.time()) + 600
                }
            }
            mock_dynamodb.Table.return_value = mock_table

            # Mock cache miss, then set
            mock_cache.get.return_value = None
            mock_cache.set.return_value = None

            response = lambda_handler(event, None)
            assert response['principalId'] == valid_jwt_payload['sub']
            assert response['policyDocument']['Statement'][0]['Effect'] == 'Allow'
            assert response['policyDocument']['Statement'][0]['Resource'] == method_arn
            assert json.loads(response['context']['permissions']) == valid_jwt_payload['permissions']
            assert response['context']['role'] == valid_jwt_payload['role']

    @pytest.mark.happy_path
    def test_valid_authorization_header_case_insensitive(self, valid_jwt_token, method_arn, valid_jwt_payload):
        """
        Test: lambda_handler accepts 'Authorization' header (case-insensitive).
        """
        event = {
            'headers': {'AUTHORIZATION': f'Bearer {valid_jwt_token}'},
            'methodArn': method_arn
        }

        with patch('authorizer.dynamodb') as mock_dynamodb, \
             patch('authorizer.token_cache') as mock_cache, \
             patch('authorizer.jwt.decode') as mock_jwt_decode:

            mock_jwt_decode.return_value = valid_jwt_payload

            mock_table = MagicMock()
            mock_table.get_item.return_value = {
                'Item': {
                    'tokenId': valid_jwt_payload['jti'],
                    'status': 'active',
                    'expiresAt': int(time.time()) + 600
                }
            }
            mock_dynamodb.Table.return_value = mock_table

            mock_cache.get.return_value = None
            mock_cache.set.return_value = None

            response = lambda_handler(event, None)
            assert response['principalId'] == valid_jwt_payload['sub']
            assert response['policyDocument']['Statement'][0]['Effect'] == 'Allow'

    @pytest.mark.happy_path
    def test_token_cache_hit(self, valid_jwt_token, method_arn, valid_jwt_payload):
        """
        Test: lambda_handler uses cached token if available.
        """
        event = {
            'authorizationToken': f'Bearer {valid_jwt_token}',
            'methodArn': method_arn
        }

        cached_context = {
            'user_id': valid_jwt_payload['sub'],
            'email': valid_jwt_payload['email'],
            'role': valid_jwt_payload['role'],
            'permissions': valid_jwt_payload['permissions'],
            'token_type': 'jwt'
        }

        with patch('authorizer.token_cache') as mock_cache:
            mock_cache.get.return_value = {
                'principal_id': valid_jwt_payload['sub'],
                'context': cached_context
            }
            # DynamoDB and jwt.decode should not be called
            with patch('authorizer.dynamodb') as mock_dynamodb, \
                 patch('authorizer.jwt.decode') as mock_jwt_decode:
                response = lambda_handler(event, None)
                assert response['principalId'] == valid_jwt_payload['sub']
                assert response['policyDocument']['Statement'][0]['Effect'] == 'Allow'
                assert json.loads(response['context']['permissions']) == valid_jwt_payload['permissions']

    # --- Edge Case Tests ---
    @pytest.mark.edge_case
    def test_missing_authorization_token(self, method_arn):
        """
        Test: lambda_handler denies access if no authorization token is provided.
        """
        event = {
            'methodArn': method_arn
        }
        response = lambda_handler(event, None)
        assert response['principalId'] == 'unauthorized'
        assert response['policyDocument']['Statement'][0]['Effect'] == 'Deny'

    @pytest.mark.edge_case
    def test_invalid_authorization_token_format(self, method_arn):
        """
        Test: lambda_handler denies access if token does not start with 'Bearer '.
        """
        event = {
            'authorizationToken': 'NotBearerToken',
            'methodArn': method_arn
        }
        response = lambda_handler(event, None)
        assert response['principalId'] == 'unauthorized'
        assert response['policyDocument']['Statement'][0]['Effect'] == 'Deny'

    @pytest.mark.edge_case
    def test_jwt_expired(self, valid_jwt_token, method_arn, valid_jwt_payload):
        """
        Test: lambda_handler denies access if JWT is expired.
        """
        event = {
            'authorizationToken': f'Bearer {valid_jwt_token}',
            'methodArn': method_arn
        }

        with patch('authorizer.token_cache') as mock_cache, \
             patch('authorizer.jwt.decode') as mock_jwt_decode:

            mock_cache.get.return_value = None
            mock_jwt_decode.side_effect = Exception("ExpiredSignatureError")

            with patch('authorizer.dynamodb') as mock_dynamodb:
                response = lambda_handler(event, None)
                assert response['principalId'] == 'unauthorized'
                assert response['policyDocument']['Statement'][0]['Effect'] == 'Deny'

    @pytest.mark.edge_case
    def test_jwt_missing_fields(self, valid_jwt_token, method_arn):
        """
        Test: lambda_handler denies access if JWT is missing 'sub' or 'jti'.
        """
        event = {
            'authorizationToken': f'Bearer {valid_jwt_token}',
            'methodArn': method_arn
        }

        incomplete_payload = {
            'exp': int(time.time()) + 600,
            'iat': int(time.time())
            # Missing 'sub' and 'jti'
        }

        with patch('authorizer.token_cache') as mock_cache, \
             patch('authorizer.jwt.decode') as mock_jwt_decode:

            mock_cache.get.return_value = None
            mock_jwt_decode.return_value = incomplete_payload

            with patch('authorizer.dynamodb') as mock_dynamodb:
                response = lambda_handler(event, None)
                assert response['principalId'] == 'unauthorized'
                assert response['policyDocument']['Statement'][0]['Effect'] == 'Deny'

    @pytest.mark.edge_case
    def test_token_revoked_in_dynamodb(self, valid_jwt_token, method_arn, valid_jwt_payload):
        """
        Test: lambda_handler denies access if token status in DynamoDB is not 'active'.
        """
        event = {
            'authorizationToken': f'Bearer {valid_jwt_token}',
            'methodArn': method_arn
        }

        with patch('authorizer.token_cache') as mock_cache, \
             patch('authorizer.jwt.decode') as mock_jwt_decode, \
             patch('authorizer.dynamodb') as mock_dynamodb:

            mock_cache.get.return_value = None
            mock_jwt_decode.return_value = valid_jwt_payload

            mock_table = MagicMock()
            mock_table.get_item.return_value = {
                'Item': {
                    'tokenId': valid_jwt_payload['jti'],
                    'status': 'revoked',
                    'expiresAt': int(time.time()) + 600
                }
            }
            mock_dynamodb.Table.return_value = mock_table

            response = lambda_handler(event, None)
            assert response['principalId'] == 'unauthorized'
            assert response['policyDocument']['Statement'][0]['Effect'] == 'Deny'

    @pytest.mark.edge_case
    def test_token_expired_in_dynamodb(self, valid_jwt_token, method_arn, valid_jwt_payload):
        """
        Test: lambda_handler denies access if token expiresAt in DynamoDB is in the past.
        """
        event = {
            'authorizationToken': f'Bearer {valid_jwt_token}',
            'methodArn': method_arn
        }

        with patch('authorizer.token_cache') as mock_cache, \
             patch('authorizer.jwt.decode') as mock_jwt_decode, \
             patch('authorizer.dynamodb') as mock_dynamodb:

            mock_cache.get.return_value = None
            mock_jwt_decode.return_value = valid_jwt_payload

            mock_table = MagicMock()
            mock_table.get_item.return_value = {
                'Item': {
                    'tokenId': valid_jwt_payload['jti'],
                    'status': 'active',
                    'expiresAt': int(time.time()) - 10  # expired
                }
            }
            mock_dynamodb.Table.return_value = mock_table

            response = lambda_handler(event, None)
            assert response['principalId'] == 'unauthorized'
            assert response['policyDocument']['Statement'][0]['Effect'] == 'Deny'

    @pytest.mark.edge_case
    def test_dynamodb_item_missing(self, valid_jwt_token, method_arn, valid_jwt_payload):
        """
        Test: lambda_handler denies access if DynamoDB returns no item for token.
        """
        event = {
            'authorizationToken': f'Bearer {valid_jwt_token}',
            'methodArn': method_arn
        }

        with patch('authorizer.token_cache') as mock_cache, \
             patch('authorizer.jwt.decode') as mock_jwt_decode, \
             patch('authorizer.dynamodb') as mock_dynamodb:

            mock_cache.get.return_value = None
            mock_jwt_decode.return_value = valid_jwt_payload

            mock_table = MagicMock()
            mock_table.get_item.return_value = {}  # No 'Item'
            mock_dynamodb.Table.return_value = mock_table

            response = lambda_handler(event, None)
            assert response['principalId'] == 'unauthorized'
            assert response['policyDocument']['Statement'][0]['Effect'] == 'Deny'

    @pytest.mark.edge_case
    def test_authorizer_response_context_serialization(self, valid_jwt_token, method_arn, valid_jwt_payload):
        """
        Test: lambda_handler serializes dict/list context fields as JSON strings.
        """
        event = {
            'authorizationToken': f'Bearer {valid_jwt_token}',
            'methodArn': method_arn
        }

        with patch('authorizer.dynamodb') as mock_dynamodb, \
             patch('authorizer.token_cache') as mock_cache, \
             patch('authorizer.jwt.decode') as mock_jwt_decode:

            mock_jwt_decode.return_value = valid_jwt_payload

            mock_table = MagicMock()
            mock_table.get_item.return_value = {
                'Item': {
                    'tokenId': valid_jwt_payload['jti'],
                    'status': 'active',
                    'expiresAt': int(time.time()) + 600
                }
            }
            mock_dynamodb.Table.return_value = mock_table

            mock_cache.get.return_value = None
            mock_cache.set.return_value = None

            response = lambda_handler(event, None)
            # permissions is a list, should be serialized as JSON string
            assert isinstance(response['context']['permissions'], str)
            assert json.loads(response['context']['permissions']) == valid_jwt_payload['permissions']