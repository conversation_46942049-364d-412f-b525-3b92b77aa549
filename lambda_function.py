"""
AWS Lambda Authorizer for API Gateway
Validates API keys, JWT tokens, and other authentication methods
"""

import json
import boto3
import logging
import jwt
import os
import time
from typing import Dict, Optional, Tuple
import hashlib
import uuid
from datetime import datetime, timezone
from enum import Enum

# Authentication error types
class AuthError(Enum):
    MISSING_TOKEN = "missing_token"
    INVALID_FORMAT = "invalid_format"
    TOKEN_EXPIRED = "token_expired"
    TOKEN_REVOKED = "token_revoked"
    INVALID_SIGNATURE = "invalid_signature"
    API_KEY_NOT_FOUND = "api_key_not_found"
    API_KEY_INACTIVE = "api_key_inactive"
    API_KEY_EXPIRED = "api_key_expired"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    SERVICE_UNAVAILABLE = "service_unavailable"
    VALIDATION_FAILED = "validation_failed"

class AuthException(Exception):
    """Custom authentication exception with error codes"""
    def __init__(self, error_type: AuthError, message: str, details: Dict = None):
        self.error_type = error_type
        self.message = message
        self.details = details or {}
        super().__init__(message)

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Enhanced logging with correlation IDs
def log_auth_event(level: str, message: str, correlation_id: str = None,
                   error_type: AuthError = None, details: Dict = None):
    """Enhanced logging with structured data for authentication events"""
    log_data = {
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'correlation_id': correlation_id or str(uuid.uuid4()),
        'message': message,
        'component': 'lambda_authorizer',
        'version': '2.0'  # Version for log format tracking
    }

    if error_type:
        log_data['error_type'] = error_type.value
        log_data['error_category'] = _get_error_category(error_type)

    if details:
        log_data.update(details)

    log_message = json.dumps(log_data)

    if level.upper() == 'INFO':
        logger.info(log_message)
    elif level.upper() == 'WARNING':
        logger.warning(log_message)
    elif level.upper() == 'ERROR':
        logger.error(log_message)
    else:
        logger.debug(log_message)

def _get_error_category(error_type: AuthError) -> str:
    """Categorize errors for monitoring and alerting"""
    token_errors = {AuthError.TOKEN_EXPIRED, AuthError.TOKEN_REVOKED, AuthError.INVALID_SIGNATURE}
    api_key_errors = {AuthError.API_KEY_NOT_FOUND, AuthError.API_KEY_INACTIVE, AuthError.API_KEY_EXPIRED}
    format_errors = {AuthError.MISSING_TOKEN, AuthError.INVALID_FORMAT}
    service_errors = {AuthError.SERVICE_UNAVAILABLE, AuthError.VALIDATION_FAILED}

    if error_type in token_errors:
        return 'token_error'
    elif error_type in api_key_errors:
        return 'api_key_error'
    elif error_type in format_errors:
        return 'format_error'
    elif error_type == AuthError.RATE_LIMIT_EXCEEDED:
        return 'rate_limit_error'
    elif error_type in service_errors:
        return 'service_error'
    else:
        return 'unknown_error'

def log_authentication_metrics(auth_type: str, success: bool, processing_time_ms: float,
                              correlation_id: str = None, error_type: AuthError = None):
    """Log authentication metrics for monitoring and alerting"""
    metrics_data = {
        'metric_type': 'authentication',
        'auth_type': auth_type,
        'success': success,
        'processing_time_ms': processing_time_ms,
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'correlation_id': correlation_id
    }

    if error_type:
        metrics_data['error_type'] = error_type.value
        metrics_data['error_category'] = _get_error_category(error_type)

    # Log as structured metric for CloudWatch/monitoring systems
    logger.info(f"METRIC: {json.dumps(metrics_data)}")

def log_security_event(event_type: str, severity: str, details: Dict, correlation_id: str = None):
    """Log security-related events for monitoring and alerting"""
    security_data = {
        'event_type': 'security',
        'security_event': event_type,
        'severity': severity,
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'correlation_id': correlation_id or str(uuid.uuid4()),
        'component': 'lambda_authorizer'
    }

    security_data.update(details)

    # Log as structured security event
    logger.warning(f"SECURITY: {json.dumps(security_data)}")

# Initialize AWS clients
dynamodb = boto3.resource('dynamodb')
secretsmanager = boto3.client('secretsmanager')

# Environment variables
API_KEYS_TABLE = os.environ.get('API_KEYS_TABLE', 'api_keys')
# JWT_SECRET_NAME should be set by Terraform with environment suffix
# Default fallback includes environment variable or assumes 'dev' environment
JWT_SECRET_NAME = os.environ.get('JWT_SECRET_NAME', 
    f"notification-platform/jwt-secret-{os.environ.get('ENVIRONMENT', 'dev')}")
TOKEN_CACHE_TTL = int(os.environ.get('TOKEN_CACHE_TTL', '300'))  # 5 minutes

# Standardized caching
class TokenCache:
    def __init__(self):
        self.cache = {}

    def get(self, key):
        item = self.cache.get(key)
        if item and item['expires'] > time.time():
            return item['value']
        elif item:
            # Remove expired item
            del self.cache[key]
        return None

    def set(self, key, value, ttl):
        self.cache[key] = {
            'value': value,
            'expires': time.time() + ttl
        }

    def clear_expired(self):
        """Remove all expired items from cache"""
        current_time = time.time()
        expired_keys = [key for key, item in self.cache.items() if item['expires'] <= current_time]
        for key in expired_keys:
            del self.cache[key]

# Initialize caches
token_cache = TokenCache()
jwt_secret_cache = TokenCache()

def lambda_handler(event, context):
    """
    Enhanced Lambda Authorizer for API Gateway with comprehensive logging and error handling
    Validates API keys, JWT tokens, and other authentication methods
    """
    correlation_id = str(uuid.uuid4())
    start_time = time.time()

    # Log incoming request
    log_auth_event('INFO', 'Authentication request received', correlation_id,
                 details={
                     'method_arn': event.get('methodArn', ''),
                     'request_id': context.aws_request_id if context else None,
                     'source_ip': event.get('requestContext', {}).get('identity', {}).get('sourceIp'),
                     'user_agent': event.get('requestContext', {}).get('identity', {}).get('userAgent')
                 })

    try:
        # Extract authorization info from event
        auth_token = extract_auth_token(event)
        method_arn = event['methodArn']

        if not auth_token:
            log_auth_event('WARNING', 'No authorization token provided', correlation_id, AuthError.MISSING_TOKEN,
                         {'method_arn': method_arn})
            raise AuthException(AuthError.MISSING_TOKEN, 'No authorization token provided')

        # Determine auth type and validate
        token_preview = auth_token[:10] + "..." if len(auth_token) > 10 else auth_token

        if auth_token.startswith('Bearer '):
            # JWT token authentication
            jwt_token = auth_token[7:]  # Remove 'Bearer ' prefix
            log_auth_event('INFO', 'Processing JWT token authentication', correlation_id,
                         details={'token_type': 'jwt', 'token_preview': token_preview})
            principal_id, context_data = validate_jwt_token(jwt_token)

        elif auth_token.startswith('ApiKey '):
            # API Key authentication
            api_key = auth_token[7:]  # Remove 'ApiKey ' prefix
            log_auth_event('INFO', 'Processing API key authentication', correlation_id,
                         details={'token_type': 'api_key', 'token_preview': token_preview})
            principal_id, context_data = validate_api_key(api_key)

        elif len(auth_token) == 64 and auth_token.isalnum():
            # Plain API key (64 char alphanumeric)
            log_auth_event('INFO', 'Processing plain API key authentication', correlation_id,
                         details={'token_type': 'plain_api_key', 'token_preview': token_preview})
            principal_id, context_data = validate_api_key(auth_token)

        else:
            log_auth_event('WARNING', 'Invalid auth token format', correlation_id, AuthError.INVALID_FORMAT,
                         {'token_preview': token_preview, 'token_length': len(auth_token)})
            raise AuthException(AuthError.INVALID_FORMAT, 'Invalid authorization token format')

        # Generate policy allowing access
        policy = create_authorizer_response(principal_id, 'Allow', method_arn, context_data)

        # Log successful authentication with metrics
        processing_time = round((time.time() - start_time) * 1000, 2)  # milliseconds
        auth_type = context_data.get('token_type', 'unknown')

        log_auth_event('INFO', 'Authentication successful', correlation_id,
                     details={
                         'principal_id': principal_id,
                         'token_type': auth_type,
                         'processing_time_ms': processing_time,
                         'method_arn': method_arn
                     })

        # Log authentication metrics
        log_authentication_metrics(auth_type, True, processing_time, correlation_id)

        return policy

    except AuthException as e:
        # Handle custom authentication exceptions with specific error codes
        processing_time = round((time.time() - start_time) * 1000, 2)
        auth_type = e.details.get('token_type', 'unknown')

        log_auth_event('WARNING', f'Authentication failed: {e.message}', correlation_id, e.error_type,
                     {**e.details, 'processing_time_ms': processing_time})

        # Log authentication failure metrics
        log_authentication_metrics(auth_type, False, processing_time, correlation_id, e.error_type)

        # Log security event for certain error types
        if e.error_type in [AuthError.TOKEN_EXPIRED, AuthError.API_KEY_EXPIRED, AuthError.INVALID_SIGNATURE]:
            log_security_event('authentication_failure', 'medium', {
                'error_type': e.error_type.value,
                'source_ip': event.get('requestContext', {}).get('identity', {}).get('sourceIp'),
                'user_agent': event.get('requestContext', {}).get('identity', {}).get('userAgent'),
                'method_arn': event.get('methodArn')
            }, correlation_id)

        # Return explicit deny policy with error context
        return create_authorizer_response('unauthorized', 'Deny', event['methodArn'], {
            'error_type': e.error_type.value,
            'error_message': e.message,
            'correlation_id': correlation_id
        })

    except Exception as e:
        # Handle unexpected errors
        processing_time = round((time.time() - start_time) * 1000, 2)
        log_auth_event('ERROR', f'Unexpected authentication error: {str(e)}', correlation_id,
                     AuthError.SERVICE_UNAVAILABLE,
                     {'error': str(e), 'processing_time_ms': processing_time})

        # Log authentication failure metrics
        log_authentication_metrics('unknown', False, processing_time, correlation_id, AuthError.SERVICE_UNAVAILABLE)

        # Log security event for unexpected errors
        log_security_event('service_error', 'high', {
            'error': str(e),
            'source_ip': event.get('requestContext', {}).get('identity', {}).get('sourceIp'),
            'method_arn': event.get('methodArn')
        }, correlation_id)

        # Return explicit deny policy
        return create_authorizer_response('unauthorized', 'Deny', event['methodArn'], {
            'error_type': AuthError.SERVICE_UNAVAILABLE.value,
            'error_message': 'Authentication service error',
            'correlation_id': correlation_id
        })

def extract_auth_token(event: Dict) -> Optional[str]:
    """
    Extract authentication token from various possible locations
    """
    # Check Authorization header
    auth_header = event.get('authorizationToken')
    if auth_header:
        return auth_header
    
    # Check headers in request context (for REQUEST authorizer)
    headers = event.get('headers', {})
    
    # Case-insensitive header lookup
    for key, value in headers.items():
        if key.lower() == 'authorization':
            return value
        elif key.lower() == 'x-api-key':
            return f"ApiKey {value}"
    
    # Check query parameters
    query_params = event.get('queryStringParameters') or {}
    if 'api_key' in query_params:
        return f"ApiKey {query_params['api_key']}"
    
    return None

def validate_jwt_token(token: str) -> Tuple[str, Dict]:
    """
    Validate JWT token with enhanced expiration checks and detailed logging
    """
    correlation_id = str(uuid.uuid4())
    token_preview = token[:10] + "..." if len(token) > 10 else token

    try:
        # Check cache first
        cache_key = hashlib.sha256(token.encode()).hexdigest()
        cached_data = token_cache.get(cache_key)
        if cached_data:
            log_auth_event('INFO', 'JWT token validated from cache', correlation_id,
                         details={'token_preview': token_preview, 'principal_id': cached_data['principal_id']})
            return cached_data['principal_id'], cached_data['context']

        # Get JWT secret from AWS Secrets Manager
        jwt_secret = get_jwt_secret()

        # First decode without verification to check expiration details
        try:
            unverified_payload = jwt.decode(token, options={"verify_signature": False})
            exp_timestamp = unverified_payload.get('exp')
            iat_timestamp = unverified_payload.get('iat')
            current_timestamp = int(time.time())

            # Enhanced expiration logging
            if exp_timestamp:
                exp_datetime = datetime.fromtimestamp(exp_timestamp, timezone.utc)
                time_until_expiry = exp_timestamp - current_timestamp

                log_auth_event('INFO', 'JWT token expiration check', correlation_id,
                             details={
                                 'token_preview': token_preview,
                                 'expires_at': exp_datetime.isoformat(),
                                 'time_until_expiry_seconds': time_until_expiry,
                                 'issued_at': datetime.fromtimestamp(iat_timestamp, timezone.utc).isoformat() if iat_timestamp else None
                             })

                # Check if token is close to expiry (within 5 minutes)
                if 0 < time_until_expiry <= 300:
                    log_auth_event('WARNING', 'JWT token expiring soon', correlation_id,
                                 details={'token_preview': token_preview, 'expires_in_seconds': time_until_expiry})
        except Exception as decode_error:
            log_auth_event('WARNING', 'Failed to decode JWT for expiration check', correlation_id,
                         details={'token_preview': token_preview, 'error': str(decode_error)})

        # Decode and validate JWT with full verification
        payload = jwt.decode(
            token,
            jwt_secret,
            algorithms=['HS256'],
            options={
                'verify_exp': True,
                'verify_iat': True,
                'verify_signature': True,
                'verify_nbf': True  # Not before validation
            }
        )

        # Extract user information
        principal_id = payload.get('sub') or payload.get('user_id')
        if not principal_id:
            raise AuthException(AuthError.VALIDATION_FAILED, "Token missing subject/user_id",
                              {'token_preview': token_preview})

        # Additional security validations
        jti = payload.get('jti')  # JWT ID for tracking
        aud = payload.get('aud')  # Audience validation
        iss = payload.get('iss')  # Issuer validation

        # Prepare context data to pass to backend
        context_data = {
            'user_id': principal_id,
            'email': payload.get('email', ''),
            'role': payload.get('role', 'user'),
            'permissions': payload.get('permissions', []),
            'client_id': payload.get('client_id', ''),
            'token_type': 'jwt',
            'jti': jti,
            'aud': aud,
            'iss': iss,
            'correlation_id': correlation_id
        }

        # Cache valid token
        token_cache.set(cache_key, {
            'principal_id': principal_id,
            'context': context_data
        }, TOKEN_CACHE_TTL)

        log_auth_event('INFO', 'JWT token validation successful', correlation_id,
                     details={'principal_id': principal_id, 'token_preview': token_preview})

        return principal_id, context_data

    except jwt.ExpiredSignatureError as e:
        log_auth_event('WARNING', 'JWT token expired', correlation_id, AuthError.TOKEN_EXPIRED,
                     {'token_preview': token_preview, 'error': str(e)})
        raise AuthException(AuthError.TOKEN_EXPIRED, 'JWT token has expired')
    except jwt.InvalidSignatureError as e:
        log_auth_event('WARNING', 'JWT token invalid signature', correlation_id, AuthError.INVALID_SIGNATURE,
                     {'token_preview': token_preview, 'error': str(e)})
        raise AuthException(AuthError.INVALID_SIGNATURE, 'JWT token signature is invalid')
    except jwt.InvalidTokenError as e:
        log_auth_event('WARNING', 'JWT token invalid', correlation_id, AuthError.VALIDATION_FAILED,
                     {'token_preview': token_preview, 'error': str(e)})
        raise AuthException(AuthError.VALIDATION_FAILED, f'Invalid JWT token: {str(e)}')
    except AuthException:
        raise  # Re-raise custom auth exceptions
    except Exception as e:
        log_auth_event('ERROR', 'JWT validation unexpected error', correlation_id, AuthError.SERVICE_UNAVAILABLE,
                     {'token_preview': token_preview, 'error': str(e)})
        raise AuthException(AuthError.SERVICE_UNAVAILABLE, 'JWT validation service error')

def validate_api_key(api_key: str) -> Tuple[str, Dict]:
    """
    Validate API key with enhanced expiration checks and detailed logging
    """
    correlation_id = str(uuid.uuid4())
    api_key_preview = api_key[:8] + "..." if len(api_key) > 8 else api_key

    try:
        # Hash API key for lookup (for security)
        api_key_hash = hashlib.sha256(api_key.encode()).hexdigest()

        # Check cache first
        cached_data = token_cache.get(api_key_hash)
        if cached_data:
            log_auth_event('INFO', 'API key validated from cache', correlation_id,
                         details={'api_key_preview': api_key_preview, 'principal_id': cached_data['principal_id']})
            return cached_data['principal_id'], cached_data['context']

        # Query DynamoDB for API key
        table = dynamodb.Table(API_KEYS_TABLE)
        response = table.get_item(
            Key={'api_key_hash': api_key_hash}
        )

        if 'Item' not in response:
            log_auth_event('WARNING', 'API key not found in database', correlation_id, AuthError.API_KEY_NOT_FOUND,
                         {'api_key_preview': api_key_preview})
            raise AuthException(AuthError.API_KEY_NOT_FOUND, 'API key not found')

        api_key_data = response['Item']

        # Check if API key is active
        if not api_key_data.get('is_active', False):
            log_auth_event('WARNING', 'API key is inactive', correlation_id, AuthError.API_KEY_INACTIVE,
                         {'api_key_preview': api_key_preview, 'client_id': api_key_data.get('client_id')})
            raise AuthException(AuthError.API_KEY_INACTIVE, 'API key is inactive')

        # Enhanced expiration check with detailed logging
        current_timestamp = int(time.time())
        if 'expires_at' in api_key_data:
            expires_at = int(api_key_data['expires_at'])
            expires_datetime = datetime.fromtimestamp(expires_at, timezone.utc)
            time_until_expiry = expires_at - current_timestamp

            log_auth_event('INFO', 'API key expiration check', correlation_id,
                         details={
                             'api_key_preview': api_key_preview,
                             'expires_at': expires_datetime.isoformat(),
                             'time_until_expiry_seconds': time_until_expiry,
                             'created_at': api_key_data.get('created_at'),
                             'last_used': api_key_data.get('last_used')
                         })

            if expires_at < current_timestamp:
                log_auth_event('WARNING', 'API key has expired', correlation_id, AuthError.API_KEY_EXPIRED,
                             {'api_key_preview': api_key_preview, 'expired_at': expires_datetime.isoformat(),
                              'expired_seconds_ago': current_timestamp - expires_at})
                raise AuthException(AuthError.API_KEY_EXPIRED, f'API key expired at {expires_datetime.isoformat()}')

            # Warn if API key is expiring soon (within 7 days)
            if 0 < time_until_expiry <= 604800:  # 7 days in seconds
                log_auth_event('WARNING', 'API key expiring soon', correlation_id,
                             details={'api_key_preview': api_key_preview, 'expires_in_seconds': time_until_expiry,
                                    'expires_in_days': round(time_until_expiry / 86400, 1)})
        else:
            log_auth_event('INFO', 'API key has no expiration date', correlation_id,
                         details={'api_key_preview': api_key_preview})

        # Check rate limits
        if not check_rate_limit(api_key_data, correlation_id, api_key_preview):
            log_auth_event('WARNING', 'API key rate limit exceeded', correlation_id, AuthError.RATE_LIMIT_EXCEEDED,
                         {'api_key_preview': api_key_preview, 'rate_limit': api_key_data.get('rate_limit')})
            raise AuthException(AuthError.RATE_LIMIT_EXCEEDED, 'Rate limit exceeded for API key')

        # Prepare context data
        principal_id = api_key_data.get('client_id') or api_key_data.get('user_id')
        if not principal_id:
            log_auth_event('ERROR', 'API key missing principal ID', correlation_id, AuthError.VALIDATION_FAILED,
                         {'api_key_preview': api_key_preview})
            raise AuthException(AuthError.VALIDATION_FAILED, 'API key missing principal ID')

        context_data = {
            'client_id': principal_id,
            'client_name': api_key_data.get('client_name', ''),
            'tier': api_key_data.get('tier', 'basic'),
            'permissions': api_key_data.get('permissions', []),
            'rate_limit': api_key_data.get('rate_limit', 1000),
            'token_type': 'api_key',
            'correlation_id': correlation_id,
            'expires_at': api_key_data.get('expires_at'),
            'created_at': api_key_data.get('created_at')
        }

        # Cache valid API key
        token_cache.set(api_key_hash, {
            'principal_id': principal_id,
            'context': context_data
        }, TOKEN_CACHE_TTL)

        # Update last used timestamp (async, don't block auth)
        update_last_used(api_key_hash, correlation_id)

        log_auth_event('INFO', 'API key validation successful', correlation_id,
                     details={'principal_id': principal_id, 'api_key_preview': api_key_preview,
                            'client_name': api_key_data.get('client_name', '')})

        return principal_id, context_data

    except AuthException:
        raise  # Re-raise custom auth exceptions
    except Exception as e:
        log_auth_event('ERROR', 'API key validation unexpected error', correlation_id, AuthError.SERVICE_UNAVAILABLE,
                     {'api_key_preview': api_key_preview, 'error': str(e)})
        raise AuthException(AuthError.SERVICE_UNAVAILABLE, 'API key validation service error')

def check_rate_limit(api_key_data: Dict, correlation_id: str = None, api_key_preview: str = None) -> bool:
    """
    Check if API key is within rate limits with enhanced logging
    Simple implementation - can be enhanced with Redis for distributed rate limiting
    """
    try:
        # Get rate limit configuration
        rate_limit = api_key_data.get('rate_limit', 1000)
        rate_window = api_key_data.get('rate_window', 3600)  # Default 1 hour window

        # In production, you'd use Redis or DynamoDB with TTL for distributed rate limiting
        # For now, we'll assume it's handled by API Gateway throttling
        # This is a placeholder for more sophisticated rate limiting logic

        log_auth_event('INFO', 'Rate limit check passed', correlation_id,
                     details={
                         'api_key_preview': api_key_preview,
                         'rate_limit': rate_limit,
                         'rate_window': rate_window,
                         'implementation': 'api_gateway_throttling'
                     })

        return True  # Simplified - always allow for now

    except Exception as e:
        log_auth_event('ERROR', 'Rate limit check error', correlation_id, AuthError.SERVICE_UNAVAILABLE,
                     {'api_key_preview': api_key_preview, 'error': str(e)})
        return False

def update_last_used(api_key_hash: str, correlation_id: str = None):
    """
    Update last used timestamp for API key with enhanced logging (async, don't block auth)
    """
    try:
        table = dynamodb.Table(API_KEYS_TABLE)
        current_timestamp = int(time.time())

        table.update_item(
            Key={'api_key_hash': api_key_hash},
            UpdateExpression='SET last_used = :timestamp',
            ExpressionAttributeValues={':timestamp': current_timestamp}
        )

        log_auth_event('INFO', 'API key last_used timestamp updated', correlation_id,
                     details={'api_key_hash_preview': api_key_hash[:16] + "...", 'timestamp': current_timestamp})

    except Exception as e:
        # Don't fail auth if update fails
        log_auth_event('WARNING', 'Failed to update API key last_used timestamp', correlation_id,
                     details={'api_key_hash_preview': api_key_hash[:16] + "...", 'error': str(e)})

def get_jwt_secret() -> str:
    """
    Get JWT secret from AWS Secrets Manager with caching
    """
    # Check cache first
    cached_secret = jwt_secret_cache.get('jwt_secret')
    if cached_secret:
        return cached_secret

    try:
        response = secretsmanager.get_secret_value(SecretId=JWT_SECRET_NAME)
        secret_data = json.loads(response['SecretString'])
        secret = secret_data.get('jwt_secret', response['SecretString'])

        # Cache for 1 hour (3600 seconds)
        jwt_secret_cache.set('jwt_secret', secret, 3600)

        return secret

    except Exception as e:
        logger.error(f"Failed to retrieve JWT secret: {str(e)}")
        raise Exception('Authentication service unavailable')

def create_authorizer_response(principal_id: str, effect: str, method_arn: str, context: Dict = None):
    """
    Create enhanced authorizer response with detailed error information and security headers
    """
    auth_response = {
        'principalId': principal_id
    }

    if effect and method_arn:
        # Create policy with appropriate effect
        auth_response['policyDocument'] = {
            'Version': '2012-10-17',
            'Statement': [
                {
                    'Action': 'execute-api:Invoke',
                    'Effect': effect,
                    'Resource': method_arn
                }
            ]
        }

        # Add additional security policies for successful authentication
        if effect == 'Allow' and context:
            # Add resource-specific permissions based on user role/permissions
            permissions = context.get('permissions', [])
            if isinstance(permissions, str):
                try:
                    permissions = json.loads(permissions)
                except:
                    permissions = []

            # Example: Add conditional access based on permissions
            if 'admin' in permissions:
                auth_response['policyDocument']['Statement'].append({
                    'Action': ['execute-api:Invoke'],
                    'Effect': 'Allow',
                    'Resource': method_arn.replace('/*', '/admin/*')
                })

    # Enhanced context with security and debugging information
    if context:
        auth_response['context'] = {}

        # Standard context fields
        for key, value in context.items():
            # API Gateway context values must be strings, numbers, or booleans
            if isinstance(value, (list, dict)):
                auth_response['context'][key] = json.dumps(value)
            elif isinstance(value, bool):
                auth_response['context'][key] = value
            elif isinstance(value, (int, float)):
                auth_response['context'][key] = value
            else:
                auth_response['context'][key] = str(value)

        # Add security headers for successful authentication
        if effect == 'Allow':
            auth_response['context']['auth_timestamp'] = str(int(time.time()))
            auth_response['context']['auth_version'] = '2.0'

            # Add rate limiting information
            if 'rate_limit' in context:
                auth_response['context']['rate_limit_remaining'] = str(context.get('rate_limit', 1000))

            # Add token expiration warning if applicable
            if 'expires_at' in context and context['expires_at']:
                try:
                    expires_at = int(context['expires_at'])
                    time_until_expiry = expires_at - int(time.time())
                    if 0 < time_until_expiry <= 86400:  # Within 24 hours
                        auth_response['context']['token_expires_soon'] = 'true'
                        auth_response['context']['expires_in_hours'] = str(round(time_until_expiry / 3600, 1))
                except:
                    pass

        # Add error details for failed authentication
        elif effect == 'Deny' and 'error_type' in context:
            auth_response['context']['auth_failure_reason'] = context.get('error_type', 'unknown')
            auth_response['context']['auth_failure_message'] = context.get('error_message', 'Authentication failed')

            # Add retry guidance based on error type
            error_type = context.get('error_type', '')
            if 'expired' in error_type.lower():
                auth_response['context']['retry_guidance'] = 'token_expired_refresh_required'
            elif 'invalid' in error_type.lower():
                auth_response['context']['retry_guidance'] = 'invalid_credentials_check_format'
            elif 'rate_limit' in error_type.lower():
                auth_response['context']['retry_guidance'] = 'rate_limited_retry_later'
            else:
                auth_response['context']['retry_guidance'] = 'authentication_failed_check_credentials'

    return auth_response

# Token Validation Utilities
def validate_token_format(token: str, token_type: str) -> Tuple[bool, str]:
    """
    Validate token format based on type with detailed error messages
    """
    if not token or not isinstance(token, str):
        return False, "Token is empty or not a string"

    token = token.strip()

    if token_type == 'jwt':
        # JWT should have 3 parts separated by dots
        parts = token.split('.')
        if len(parts) != 3:
            return False, f"JWT token should have 3 parts, found {len(parts)}"

        # Each part should be base64 encoded (basic check)
        for i, part in enumerate(parts):
            if not part or not all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=-_' for c in part):
                return False, f"JWT part {i+1} contains invalid base64 characters"

        return True, "Valid JWT format"

    elif token_type == 'api_key':
        # API key should be alphanumeric and of specific length
        if len(token) < 32:
            return False, f"API key too short: {len(token)} characters (minimum 32)"

        if len(token) > 128:
            return False, f"API key too long: {len(token)} characters (maximum 128)"

        if not token.replace('-', '').replace('_', '').isalnum():
            return False, "API key contains invalid characters (only alphanumeric, hyphens, and underscores allowed)"

        return True, "Valid API key format"

    else:
        return False, f"Unknown token type: {token_type}"

def check_token_expiration(expires_at: int, grace_period_seconds: int = 0) -> Tuple[bool, Dict]:
    """
    Check if token is expired with optional grace period and detailed timing information
    """
    current_timestamp = int(time.time())

    if expires_at <= current_timestamp - grace_period_seconds:
        expired_seconds_ago = current_timestamp - expires_at
        return False, {
            'expired': True,
            'expired_at': datetime.fromtimestamp(expires_at, timezone.utc).isoformat(),
            'expired_seconds_ago': expired_seconds_ago,
            'expired_minutes_ago': round(expired_seconds_ago / 60, 1),
            'grace_period_used': grace_period_seconds > 0
        }

    time_until_expiry = expires_at - current_timestamp
    expires_datetime = datetime.fromtimestamp(expires_at, timezone.utc)

    return True, {
        'expired': False,
        'expires_at': expires_datetime.isoformat(),
        'time_until_expiry_seconds': time_until_expiry,
        'time_until_expiry_minutes': round(time_until_expiry / 60, 1),
        'time_until_expiry_hours': round(time_until_expiry / 3600, 1),
        'expires_soon': time_until_expiry <= 3600,  # Within 1 hour
        'expires_very_soon': time_until_expiry <= 300  # Within 5 minutes
    }

def validate_jwt_claims(payload: Dict, required_claims: list = None,
                       allowed_audiences: list = None, allowed_issuers: list = None) -> Tuple[bool, str]:
    """
    Validate JWT claims with configurable requirements
    """
    required_claims = required_claims or ['sub', 'exp', 'iat']

    # Check required claims
    for claim in required_claims:
        if claim not in payload:
            return False, f"Missing required claim: {claim}"

        if not payload[claim]:
            return False, f"Empty required claim: {claim}"

    # Validate audience if specified
    if allowed_audiences:
        aud = payload.get('aud')
        if not aud:
            return False, "Missing audience claim (aud) but audience validation is required"

        # aud can be a string or list
        if isinstance(aud, str):
            aud = [aud]

        if not any(audience in allowed_audiences for audience in aud):
            return False, f"Invalid audience. Expected one of {allowed_audiences}, got {aud}"

    # Validate issuer if specified
    if allowed_issuers:
        iss = payload.get('iss')
        if not iss:
            return False, "Missing issuer claim (iss) but issuer validation is required"

        if iss not in allowed_issuers:
            return False, f"Invalid issuer. Expected one of {allowed_issuers}, got {iss}"

    # Validate timestamps
    current_time = int(time.time())

    # Check not before (nbf)
    nbf = payload.get('nbf')
    if nbf and nbf > current_time:
        return False, f"Token not yet valid (nbf: {nbf}, current: {current_time})"

    # Check issued at (iat) - shouldn't be in the future
    iat = payload.get('iat')
    if iat and iat > current_time + 300:  # 5 minute tolerance for clock skew
        return False, f"Token issued in the future (iat: {iat}, current: {current_time})"

    return True, "All claims valid"

def sanitize_token_for_logging(token: str, show_chars: int = 8) -> str:
    """
    Safely sanitize token for logging purposes
    """
    if not token:
        return "empty_token"

    if len(token) <= show_chars:
        return "*" * len(token)

    return token[:show_chars] + "..." + "*" * (len(token) - show_chars)

def detect_suspicious_patterns(token: str, source_ip: str = None, user_agent: str = None) -> Tuple[bool, list]:
    """
    Detect suspicious patterns in authentication attempts
    """
    suspicious_indicators = []

    # Check for common attack patterns in token
    if token:
        # SQL injection patterns
        sql_patterns = ['union', 'select', 'drop', 'insert', 'update', 'delete', '--', ';']
        if any(pattern in token.lower() for pattern in sql_patterns):
            suspicious_indicators.append('sql_injection_pattern')

        # XSS patterns
        xss_patterns = ['<script', 'javascript:', 'onerror=', 'onload=']
        if any(pattern in token.lower() for pattern in xss_patterns):
            suspicious_indicators.append('xss_pattern')

        # Extremely long tokens (potential buffer overflow)
        if len(token) > 8192:  # 8KB
            suspicious_indicators.append('oversized_token')

        # Repeated characters (potential fuzzing)
        if len(set(token)) < len(token) * 0.1:  # Less than 10% unique characters
            suspicious_indicators.append('low_entropy_token')

    # Check source IP patterns
    if source_ip:
        # Known bad IP ranges (this would typically come from a threat intelligence feed)
        # For demo purposes, checking for localhost/private ranges in production
        if source_ip.startswith(('127.', '10.', '192.168.', '172.')):
            suspicious_indicators.append('private_ip_range')

    # Check user agent patterns
    if user_agent:
        suspicious_agents = ['sqlmap', 'nikto', 'nmap', 'masscan', 'curl', 'wget']
        if any(agent in user_agent.lower() for agent in suspicious_agents):
            suspicious_indicators.append('suspicious_user_agent')

        # Empty or very short user agent
        if not user_agent or len(user_agent) < 10:
            suspicious_indicators.append('minimal_user_agent')

    return len(suspicious_indicators) > 0, suspicious_indicators
