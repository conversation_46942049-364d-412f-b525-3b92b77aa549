"""
AWS Lambda JWT Token Manager and Authorizer
Handles JWT token creation, validation, refresh, revocation, and session management
Stores tokens and session data in DynamoDB
"""

import json
import boto3
import logging
import jwt
import os
import time
from typing import Dict, Optional, Tuple
import hashlib
import uuid
from datetime import datetime, timezone
from enum import Enum
import secrets

# JWT Authentication error types
class AuthError(Enum):
    MISSING_TOKEN = "missing_token"
    INVALID_FORMAT = "invalid_format"
    TOKEN_EXPIRED = "token_expired"
    TOKEN_REVOKED = "token_revoked"
    INVALID_SIGNATURE = "invalid_signature"
    INVALID_CREDENTIALS = "invalid_credentials"
    SESSION_NOT_FOUND = "session_not_found"
    REFRESH_TOKEN_EXPIRED = "refresh_token_expired"
    REFRESH_TOKEN_INVALID = "refresh_token_invalid"
    SERVICE_UNAVAILABLE = "service_unavailable"
    VALIDATION_FAILED = "validation_failed"

class AuthException(Exception):
    """Custom authentication exception with error codes"""
    def __init__(self, error_type: AuthError, message: str, details: Dict = None):
        self.error_type = error_type
        self.message = message
        self.details = details or {}
        super().__init__(message)

# Enhanced logging with correlation IDs
def log_auth_event(level: str, message: str, correlation_id: str = None, 
                   error_type: AuthError = None, details: Dict = None):
    """Enhanced logging with structured data for authentication events"""
    log_data = {
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'correlation_id': correlation_id or str(uuid.uuid4()),
        'message': message,
        'component': 'lambda_authorizer',
        'version': '2.0'  # Version for log format tracking
    }
    
    if error_type:
        log_data['error_type'] = error_type.value
        log_data['error_category'] = _get_error_category(error_type)
    
    if details:
        log_data.update(details)
    
    log_message = json.dumps(log_data)
    
    if level.upper() == 'INFO':
        logger.info(log_message)
    elif level.upper() == 'WARNING':
        logger.warning(log_message)
    elif level.upper() == 'ERROR':
        logger.error(log_message)
    else:
        logger.debug(log_message)

def _get_error_category(error_type: AuthError) -> str:
    """Categorize errors for monitoring and alerting"""
    token_errors = {AuthError.TOKEN_EXPIRED, AuthError.TOKEN_REVOKED, AuthError.INVALID_SIGNATURE}
    session_errors = {AuthError.SESSION_NOT_FOUND, AuthError.REFRESH_TOKEN_EXPIRED, AuthError.REFRESH_TOKEN_INVALID}
    format_errors = {AuthError.MISSING_TOKEN, AuthError.INVALID_FORMAT}
    service_errors = {AuthError.SERVICE_UNAVAILABLE, AuthError.VALIDATION_FAILED}
    
    if error_type in token_errors:
        return 'token_error'
    elif error_type in session_errors:
        return 'session_error'
    elif error_type in format_errors:
        return 'format_error'
    elif error_type == AuthError.INVALID_CREDENTIALS:
        return 'credential_error'
    elif error_type in service_errors:
        return 'service_error'
    else:
        return 'unknown_error'

def log_authentication_metrics(auth_type: str, success: bool, processing_time_ms: float, 
                              correlation_id: str = None, error_type: AuthError = None):
    """Log authentication metrics for monitoring and alerting"""
    metrics_data = {
        'metric_type': 'authentication',
        'auth_type': auth_type,
        'success': success,
        'processing_time_ms': processing_time_ms,
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'correlation_id': correlation_id
    }
    
    if error_type:
        metrics_data['error_type'] = error_type.value
        metrics_data['error_category'] = _get_error_category(error_type)
    
    # Log as structured metric for CloudWatch/monitoring systems
    logger.info(f"METRIC: {json.dumps(metrics_data)}")

def log_security_event(event_type: str, severity: str, details: Dict, correlation_id: str = None):
    """Log security-related events for monitoring and alerting"""
    security_data = {
        'event_type': 'security',
        'security_event': event_type,
        'severity': severity,
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'correlation_id': correlation_id or str(uuid.uuid4()),
        'component': 'lambda_authorizer'
    }
    
    security_data.update(details)
    
    # Log as structured security event
    logger.warning(f"SECURITY: {json.dumps(security_data)}")

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
dynamodb = boto3.resource('dynamodb')

# Environment variables
JWT_TOKENS_TABLE = os.environ.get('JWT_TOKENS_TABLE', 'jwt_tokens')
JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'default-secret-key-change-in-production')
ACCESS_TOKEN_TTL = int(os.environ.get('ACCESS_TOKEN_TTL', '900'))  # 15 minutes
REFRESH_TOKEN_TTL = int(os.environ.get('REFRESH_TOKEN_TTL', '604800'))  # 7 days
TOKEN_CACHE_TTL = int(os.environ.get('TOKEN_CACHE_TTL', '300'))  # 5 minutes
ENVIRONMENT = os.environ.get('ENVIRONMENT', 'dev')

# Standardized caching
class TokenCache:
    def __init__(self):
        self.cache = {}

    def get(self, key):
        item = self.cache.get(key)
        if item and item['expires'] > time.time():
            return item['value']
        elif item:
            # Remove expired item
            del self.cache[key]
        return None

    def set(self, key, value, ttl):
        self.cache[key] = {
            'value': value,
            'expires': time.time() + ttl
        }

    def clear_expired(self):
        """Remove all expired items from cache"""
        current_time = time.time()
        expired_keys = [key for key, item in self.cache.items() if item['expires'] <= current_time]
        for key in expired_keys:
            del self.cache[key]

# Initialize caches
token_cache = TokenCache()

def lambda_handler(event, context):
    """
    JWT Token Manager and Authorizer for API Gateway
    Handles token creation, validation, refresh, revocation, and session management
    
    Supported operations:
    - POST /auth/login - Create new JWT tokens
    - POST /auth/refresh - Refresh access token
    - POST /auth/logout - Revoke tokens
    - GET /auth/validate - Validate token (authorizer mode)
    """
    correlation_id = str(uuid.uuid4())
    start_time = time.time()
    
    # Determine operation type
    http_method = event.get('httpMethod', 'GET')
    resource_path = event.get('resource', '')
    
    # Log incoming request
    log_auth_event('INFO', f'JWT operation request: {http_method} {resource_path}', correlation_id,
                 details={
                     'method_arn': event.get('methodArn', ''),
                     'request_id': context.aws_request_id if context else None,
                     'source_ip': event.get('requestContext', {}).get('identity', {}).get('sourceIp'),
                     'user_agent': event.get('requestContext', {}).get('identity', {}).get('userAgent'),
                     'operation': f'{http_method} {resource_path}'
                 })
    
    try:
        # Route to appropriate handler based on operation
        if 'methodArn' in event:
            # Authorizer mode - validate token
            return handle_token_validation(event, context, correlation_id, start_time)
        elif http_method == 'POST' and '/auth/login' in resource_path:
            # Login - create new tokens
            return handle_login(event, context, correlation_id, start_time)
        elif http_method == 'POST' and '/auth/refresh' in resource_path:
            # Refresh access token
            return handle_token_refresh(event, context, correlation_id, start_time)
        elif http_method == 'POST' and '/auth/logout' in resource_path:
            # Logout - revoke tokens
            return handle_logout(event, context, correlation_id, start_time)
        else:
            # Unknown operation
            raise AuthException(AuthError.VALIDATION_FAILED, f'Unsupported operation: {http_method} {resource_path}')
            
    except AuthException as e:
        # Handle custom authentication exceptions
        processing_time = round((time.time() - start_time) * 1000, 2)
        
        log_auth_event('WARNING', f'JWT operation failed: {e.message}', correlation_id, e.error_type,
                     {**e.details, 'processing_time_ms': processing_time})
        
        # Log authentication failure metrics
        log_authentication_metrics('jwt', False, processing_time, correlation_id, e.error_type)
        
        # Return appropriate error response
        if 'methodArn' in event:
            # Authorizer mode - return deny policy
            return create_authorizer_response('unauthorized', 'Deny', event['methodArn'], {
                'error_type': e.error_type.value,
                'error_message': e.message,
                'correlation_id': correlation_id
            })
        else:
            # API mode - return HTTP error response
            return create_http_response(401, {
                'error': e.error_type.value,
                'message': e.message,
                'correlation_id': correlation_id
            })
        
    except Exception as e:
        # Handle unexpected errors
        processing_time = round((time.time() - start_time) * 1000, 2)
        log_auth_event('ERROR', f'Unexpected JWT operation error: {str(e)}', correlation_id, 
                     AuthError.SERVICE_UNAVAILABLE,
                     {'error': str(e), 'processing_time_ms': processing_time})
        
        # Log failure metrics
        log_authentication_metrics('jwt', False, processing_time, correlation_id, AuthError.SERVICE_UNAVAILABLE)
        
        # Return appropriate error response
        if 'methodArn' in event:
            # Authorizer mode - return deny policy
            return create_authorizer_response('unauthorized', 'Deny', event['methodArn'], {
                'error_type': AuthError.SERVICE_UNAVAILABLE.value,
                'error_message': 'JWT service error',
                'correlation_id': correlation_id
            })
        else:
            # API mode - return HTTP error response
            return create_http_response(500, {
                'error': AuthError.SERVICE_UNAVAILABLE.value,
                'message': 'JWT service error',
                'correlation_id': correlation_id
            })

# JWT Token Management Functions

def create_jwt_tokens(user_id: str, user_data: Dict, correlation_id: str = None) -> Tuple[str, str, Dict]:
    """
    Create access and refresh JWT tokens for a user
    Returns: (access_token, refresh_token, session_data)
    """
    current_time = int(time.time())
    session_id = str(uuid.uuid4())

    # Create access token payload
    access_payload = {
        'sub': user_id,
        'jti': str(uuid.uuid4()),
        'session_id': session_id,
        'iat': current_time,
        'exp': current_time + ACCESS_TOKEN_TTL,
        'type': 'access',
        'email': user_data.get('email', ''),
        'role': user_data.get('role', 'user'),
        'permissions': user_data.get('permissions', []),
        'iss': f'jwt-auth-{ENVIRONMENT}',
        'aud': 'api-gateway'
    }

    # Create refresh token payload
    refresh_payload = {
        'sub': user_id,
        'jti': str(uuid.uuid4()),
        'session_id': session_id,
        'iat': current_time,
        'exp': current_time + REFRESH_TOKEN_TTL,
        'type': 'refresh',
        'iss': f'jwt-auth-{ENVIRONMENT}',
        'aud': 'api-gateway'
    }

    # Generate tokens
    access_token = jwt.encode(access_payload, JWT_SECRET_KEY, algorithm='HS256')
    refresh_token = jwt.encode(refresh_payload, JWT_SECRET_KEY, algorithm='HS256')

    # Session data to store in DynamoDB
    session_data = {
        'session_id': session_id,
        'user_id': user_id,
        'access_token_jti': access_payload['jti'],
        'refresh_token_jti': refresh_payload['jti'],
        'created_at': current_time,
        'last_used': current_time,
        'expires_at': current_time + REFRESH_TOKEN_TTL,
        'status': 'active',
        'user_data': user_data,
        'source_ip': user_data.get('source_ip', ''),
        'user_agent': user_data.get('user_agent', '')
    }

    # Store session in DynamoDB
    store_session(session_data, correlation_id)

    log_auth_event('INFO', 'JWT tokens created successfully', correlation_id,
                 details={'user_id': user_id, 'session_id': session_id})

    return access_token, refresh_token, session_data

def refresh_jwt_token(refresh_token: str, correlation_id: str = None) -> Tuple[str, Dict]:
    """
    Refresh access token using refresh token
    Returns: (new_access_token, session_data)
    """
    try:
        # Validate refresh token
        payload = jwt.decode(refresh_token, JWT_SECRET_KEY, algorithms=['HS256'])

        if payload.get('type') != 'refresh':
            raise AuthException(AuthError.REFRESH_TOKEN_INVALID, 'Invalid refresh token type')

        session_id = payload.get('session_id')
        refresh_jti = payload.get('jti')
        user_id = payload.get('sub')

        if not all([session_id, refresh_jti, user_id]):
            raise AuthException(AuthError.REFRESH_TOKEN_INVALID, 'Missing required claims in refresh token')

        # Get session from DynamoDB
        session_data = get_session(session_id, correlation_id)

        if not session_data:
            raise AuthException(AuthError.SESSION_NOT_FOUND, 'Session not found')

        if session_data['status'] != 'active':
            raise AuthException(AuthError.TOKEN_REVOKED, 'Session is not active')

        if session_data['refresh_token_jti'] != refresh_jti:
            raise AuthException(AuthError.REFRESH_TOKEN_INVALID, 'Refresh token JTI mismatch')

        # Create new access token
        current_time = int(time.time())
        new_access_payload = {
            'sub': user_id,
            'jti': str(uuid.uuid4()),
            'session_id': session_id,
            'iat': current_time,
            'exp': current_time + ACCESS_TOKEN_TTL,
            'type': 'access',
            'email': session_data['user_data'].get('email', ''),
            'role': session_data['user_data'].get('role', 'user'),
            'permissions': session_data['user_data'].get('permissions', []),
            'iss': f'jwt-auth-{ENVIRONMENT}',
            'aud': 'api-gateway'
        }

        new_access_token = jwt.encode(new_access_payload, JWT_SECRET_KEY, algorithm='HS256')

        # Update session with new access token JTI
        update_session(session_id, {
            'access_token_jti': new_access_payload['jti'],
            'last_used': current_time
        }, correlation_id)

        log_auth_event('INFO', 'Access token refreshed successfully', correlation_id,
                     details={'user_id': user_id, 'session_id': session_id})

        return new_access_token, session_data

    except jwt.ExpiredSignatureError:
        raise AuthException(AuthError.REFRESH_TOKEN_EXPIRED, 'Refresh token has expired')
    except jwt.InvalidTokenError as e:
        raise AuthException(AuthError.REFRESH_TOKEN_INVALID, f'Invalid refresh token: {str(e)}')

def revoke_session(session_id: str, correlation_id: str = None) -> bool:
    """
    Revoke a session (logout)
    """
    try:
        # Update session status to revoked
        update_session(session_id, {
            'status': 'revoked',
            'revoked_at': int(time.time())
        }, correlation_id)

        log_auth_event('INFO', 'Session revoked successfully', correlation_id,
                     details={'session_id': session_id})

        return True

    except Exception as e:
        log_auth_event('ERROR', 'Failed to revoke session', correlation_id,
                     details={'session_id': session_id, 'error': str(e)})
        return False

# DynamoDB Session Management Functions

def store_session(session_data: Dict, correlation_id: str = None) -> bool:
    """Store session data in DynamoDB"""
    try:
        table = dynamodb.Table(JWT_TOKENS_TABLE)
        session_data['ttl'] = session_data['expires_at'] + 86400  # 24 hours after expiration
        table.put_item(Item=session_data)
        log_auth_event('INFO', 'Session stored successfully', correlation_id,
                     details={'session_id': session_data['session_id']})
        return True
    except Exception as e:
        log_auth_event('ERROR', 'Failed to store session', correlation_id,
                     details={'session_id': session_data.get('session_id'), 'error': str(e)})
        return False

def get_session(session_id: str, correlation_id: str = None) -> Optional[Dict]:
    """Retrieve session data from DynamoDB"""
    try:
        table = dynamodb.Table(JWT_TOKENS_TABLE)
        response = table.get_item(Key={'session_id': session_id})

        if 'Item' not in response:
            return None

        session_data = response['Item']
        current_time = int(time.time())
        if session_data.get('expires_at', 0) < current_time:
            return None

        return session_data
    except Exception as e:
        log_auth_event('ERROR', 'Failed to retrieve session', correlation_id,
                     details={'session_id': session_id, 'error': str(e)})
        return None

def update_session(session_id: str, updates: Dict, correlation_id: str = None) -> bool:
    """Update session data in DynamoDB"""
    try:
        table = dynamodb.Table(JWT_TOKENS_TABLE)
        update_expression = "SET "
        expression_values = {}

        for key, value in updates.items():
            update_expression += f"{key} = :{key}, "
            expression_values[f":{key}"] = value

        update_expression = update_expression.rstrip(", ")

        table.update_item(
            Key={'session_id': session_id},
            UpdateExpression=update_expression,
            ExpressionAttributeValues=expression_values
        )
        return True
    except Exception as e:
        log_auth_event('ERROR', 'Failed to update session', correlation_id,
                     details={'session_id': session_id, 'error': str(e)})
        return False
